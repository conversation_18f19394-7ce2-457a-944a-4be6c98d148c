import request from '@/utils/request'
import type { 
  Country, 
  CertType, 
  SysDept, 
  SysUser, 
  AjaxResult, 
  TableDataInfo 
} from '@/types/common'

// ==================== TypeScript 接口定义 ====================

/** 文件夹查询请求DTO */
export interface FolderQueryDTO {
  /** 页码 */
  pageNum?: number
  /** 页大小 */
  pageSize?: number
  /** 文件夹状态 (unassociated/associated) */
  status?: string
  /** 版本ID */
  versionId?: string
  /** 任务ID */
  taskId?: string
  /** 文件夹类型 (standard/regular) */
  folderType?: string
  /** 国家ID */
  countryId?: number
  /** 国家代码 */
  countryCode?: string
  /** 证件类型ID */
  certTypeId?: number
  /** 证件类型代码 */
  certTypeCode?: string
  /** 发行年份 */
  issueYear?: string
  /** 部门ID */
  deptId?: number
  /** 上传者ID */
  uploaderId?: number
  /** 关键词搜索 */
  keyword?: string
}

/** 文件夹类型更新请求DTO */
export interface FolderTypeUpdateDTO {
  /** 新类型 ("standard" 或 "regular") */
  newType: 'standard' | 'regular'
}

/** 版本关联请求DTO */
export interface VersionAssociationDTO {
  /** 版本ID */
  versionId: string
}

/** 第三方检测结果DTO */
export interface ThirdPartyDetectionResultDTO {
  /** 检测ID */
  detectionId: string
  /** 检测时间 */
  detectionTime: string
  /** 检测状态 (success/no_match/failed) */
  detectionStatus: 'success' | 'no_match' | 'failed'
  /** 置信度 (0-1) */
  confidence?: number
  /** 匹配的版本列表 */
  matchedVersions?: VersionMatchDTO[]
  /** 错误信息 */
  errorMessage?: string
}

/** 版本匹配信息DTO */
export interface VersionMatchDTO {
  /** 版本ID */
  versionId: string
  /** 版本代码 */
  versionCode: string
  /** 匹配分数 (0-1) */
  matchScore: number
  /** 匹配原因 */
  matchReason: string
}

/** 版本审核请求DTO */
export interface VersionReviewDTO {
  /** 审核结果 (approved/rejected) */
  reviewResult: 'approved' | 'rejected'
  /** 审核意见 */
  reviewComment?: string
  /** 审核人 */
  reviewedBy?: string
}





/** 预解析版本信息 */
export interface PreParseVersionInfo {
  /** 原始文件夹名称 */
  originalFolderName?: string
  /** 解析后的版本代码 */
  parsedVersionCode?: string
  /** 解析的国家名称 */
  countryName?: string
  /** 匹配的国家代码 */
  countryCode?: string
  /** 解析的证件类型名称 */
  certTypeName?: string
  /** 匹配的证件类型代码 */
  certTypeCode?: string
  /** 签发年份 */
  issueYear?: string
  /** 证件号前缀 */
  certNumberPrefix?: string
  /** 签发地 */
  issuePlace?: string
  /** 解析状态: SUCCESS, PARTIAL, FAILED */
  parseStatus?: 'SUCCESS' | 'PARTIAL' | 'FAILED'
  /** 解析错误信息 */
  parseErrors?: string[]
  /** 解析时间 */
  parseTime?: string
}

/** 版本信息 */
export interface VersionInfo {
  /** 版本ID */
  versionId: string
  /** 版本代码 */
  versionCode: string
  /** 标准样本文件夹ID */
  standardFolderId?: string
  /** 版本状态 */
  status: string
}

/** 文件夹信息响应VO */
export interface FolderInfoVO {
  /** MongoDB主键 */
  id: string
  /** 业务文件夹ID */
  folderId: string
  /** 关联任务ID */
  taskId?: string
  /** 关联版本ID */
  versionId?: string
  /** 文件夹类型 */
  folderType: string
  /** 文件夹名称 */
  folderName: string
  /** 主图路径 */
  mainPicPath?: string
  /** 国家信息 */
  countryInfo: Country
  /** 证件类型信息 */
  certInfo: CertType
  /** 发行年份 */
  issueYear: string
  /** 文件数量 */
  fileCount: number
  /** 已处理文件数量 */
  processedFileCount: number
  /** 状态 */
  status: string
  /** 审核状态 */
  reviewStatus?: string
  /** 预解析版本信息 */
  preParseVersionInfo?: PreParseVersionInfo
  /** 版本信息 */
  versionInfo?: VersionInfo
  /** 部门信息 */
  deptInfo: SysDept
  /** 上传者信息 */
  uploaderInfo: SysUser
  /** 创建时间 */
  createTime: string
  /** 更新时间 */
  updateTime: string
}

/** 统计信息 */
export interface StatisticsInfo {
  [key: string]: any
}

/** 标准样本管理请求DTO */
export interface StandardSampleRequestDTO {
  /** 版本ID */
  versionId: string
  /** 文件夹ID */
  folderId: string
  /** 是否保留现有标注（替换标准样本时使用） */
  keepAnnotations?: boolean
  /** 操作原因（可选） */
  reason?: string
}

/** 标准样本信息响应VO */
export interface StandardSampleInfoVO {
  /** 文件夹ID */
  folderId: string
  /** 文件夹名称 */
  folderName?: string
  /** 版本ID */
  versionId: string
  /** 是否为标准样本 */
  isStandardSample: boolean
  /** 是否可设置为标准样本 */
  canSetAsStandard: boolean
  /** 是否可取消标准样本 */
  canRemoveStandard: boolean
  /** 状态说明 */
  reason: string
  /** 模板数量 */
  templateCount?: number
  /** 可标注图片数量 */
  annotatableImageCount?: number
  /** 总图片数量 */
  totalImageCount?: number
}

// ==================== API 函数 ====================

/**
 * 关联文件夹到已有版本
 * POST /api/folders/{folderId}/associate
 */
export function associateFolderToVersion(folderId: string, data: VersionAssociationDTO) {
  return request({
    url: `/api/folders/${folderId}/associate`,
    method: 'post',
    data
  })
}

/**
 * 查询文件夹列表
 * GET /api/folders
 */
export function getFolderList(params?: FolderQueryDTO) {
  return request({
    url: '/api/folders',
    method: 'get',
    params
  })
}

/**
 * 获取文件夹详情
 * GET /api/folders/{folderId}
 */
export function getFolderDetails(folderId: string) {
  return request({
    url: `/api/folders/${folderId}`,
    method: 'get'
  })
}

/**
 * 获取文件夹统计信息
 * GET /api/folders/{folderId}/statistics
 */
export function getFolderStatistics(folderId: string) {
  return request({
    url: `/api/folders/${folderId}/statistics`,
    method: 'get'
  })
}

/**
 * 获取任务的文件夹统计
 * GET /api/folders/task/{taskId}/statistics
 */
export function getTaskFolderStatistics(taskId: string) {
  return request({
    url: `/api/folders/task/${taskId}/statistics`,
    method: 'get'
  })
}

/**
 * 删除文件夹
 * DELETE /api/folders/{folderId}
 */
export function deleteFolder(folderId: string) {
  return request({
    url: `/api/folders/${folderId}`,
    method: 'delete'
  })
}

// ==================== 版本审核和样本分类相关API ====================

/**
 * 第三方版本检测
 *
 * 调用第三方服务对指定文件夹进行版本检测，返回匹配的版本信息
 *
 * @param folderId - 文件夹ID
 * @returns Promise<AjaxResult<ThirdPartyDetectionResultDTO>> 检测结果
 *
 * @example
 * ```typescript
 * const result = await detectVersionByThirdParty('folder-123')
 * if (result.code === 200) {
 *   console.log('检测结果:', result.data)
 * }
 * ```
 */
export function detectVersionByThirdParty(folderId: string): Promise<AjaxResult<ThirdPartyDetectionResultDTO>> {
  return request({
    url: `/api/folders/${folderId}/detect-version`,
    method: 'post'
  })
}

/**
 * 获取待审核文件夹列表
 *
 * 查询所有需要人工审核的文件夹，支持分页和条件筛选
 *
 * @param params - 查询参数，可选
 * @returns Promise<TableDataInfo<FolderInfoVO[]>> 分页的文件夹列表
 *
 * @example
 * ```typescript
 * const result = await getPendingReviewFolders({ pageNum: 1, pageSize: 10 })
 * if (result.code === 200) {
 *   console.log('待审核文件夹:', result.rows)
 * }
 * ```
 */
export function getPendingReviewFolders(params?: FolderQueryDTO): Promise<TableDataInfo<FolderInfoVO[]>> {
  return request({
    url: '/api/folders/pending-review',
    method: 'get',
    params
  })
}

/**
 * 审核文件夹版本关联
 *
 * 对文件夹的版本关联进行人工审核，可以批准或拒绝
 *
 * @param folderId - 文件夹ID
 * @param data - 审核数据，包含审核结果和意见
 * @returns Promise<AjaxResult<void>> 审核结果
 *
 * @example
 * ```typescript
 * const reviewData: VersionReviewDTO = {
 *   reviewResult: 'approved',
 *   reviewComment: '版本关联正确',
 *   reviewedBy: 'admin'
 * }
 * const result = await reviewFolderVersion('folder-123', reviewData)
 * if (result.code === 200) {
 *   console.log('审核成功')
 * }
 * ```
 */
export function reviewFolderVersion(folderId: string, data: VersionReviewDTO): Promise<AjaxResult<void>> {
  return request({
    url: `/api/folders/${folderId}/review`,
    method: 'post',
    data
  })
}



// ==================== 版本解析相关API ====================

/**
 * 重新解析单个文件夹的版本信息
 * POST /system/version-parse/reparse-folder/{folderId}
 */
export function reparseFolder(folderId: string): Promise<AjaxResult<void>> {
  return request({
    url: `/system/version-parse/reparse-folder/${folderId}`,
    method: 'post'
  })
}

/**
 * 批量重新解析任务下的所有文件夹
 * POST /system/version-parse/reparse-task/{taskId}
 */
export function reparseAllFolders(taskId: string): Promise<AjaxResult<void>> {
  return request({
    url: `/system/version-parse/reparse-task/${taskId}`,
    method: 'post'
  })
}

/**
 * 获取任务的解析统计信息
 * GET /system/version-parse/statistics/{taskId}
 */
export function getParseStatistics(taskId: string): Promise<AjaxResult<any>> {
  return request({
    url: `/system/version-parse/statistics/${taskId}`,
    method: 'get'
  })
}

/**
 * 手动触发任务的版本解析
 * POST /system/version-parse/trigger-parse/{taskId}
 */
export function triggerParse(taskId: string): Promise<AjaxResult<void>> {
  return request({
    url: `/system/version-parse/trigger-parse/${taskId}`,
    method: 'post'
  })
}

/**
 * 测试解析文件夹名称
 * GET /system/version-parse/test-parse
 */
export function testParseFolderName(folderName: string): Promise<AjaxResult<any>> {
  return request({
    url: '/system/version-parse/test-parse',
    method: 'get',
    params: { folderName }
  })
}

/**
 * 更新文件夹信息
 * PUT /api/folders/{folderId}
 */
export function updateFolderInfo(folderId: string, data: any): Promise<AjaxResult<void>> {
  return request({
    url: `/api/folders/${folderId}`,
    method: 'put',
    data
  })
}

// ==================== 标准样本管理API ====================

/**
 * 设置标准样本文件夹
 * POST /api/folders/{folderId}/set-standard
 */
export function setStandardFolder(folderId: string, data: StandardSampleRequestDTO): Promise<AjaxResult> {
  return request({
    url: `/api/folders/${folderId}/set-standard`,
    method: 'post',
    data
  })
}

/**
 * 替换标准样本文件夹
 * POST /api/folders/{folderId}/replace-standard
 */
export function replaceStandardFolder(folderId: string, data: StandardSampleRequestDTO): Promise<AjaxResult> {
  return request({
    url: `/api/folders/${folderId}/replace-standard`,
    method: 'post',
    data
  })
}

/**
 * 取消标准样本文件夹
 * POST /api/folders/{folderId}/remove-standard
 */
export function removeStandardFolder(folderId: string, data: StandardSampleRequestDTO): Promise<AjaxResult> {
  return request({
    url: `/api/folders/${folderId}/remove-standard`,
    method: 'post',
    data
  })
}

/**
 * 获取文件夹标准样本信息
 * GET /api/folders/{folderId}/standard-info
 */
export function getStandardSampleInfo(folderId: string): Promise<AjaxResult<StandardSampleInfoVO>> {
  return request({
    url: `/api/folders/${folderId}/standard-info`,
    method: 'get'
  })
}

/**
 * 获取版本的标准样本文件夹
 * GET /api/folders/version/{versionId}/standard
 */
export function getVersionStandardFolder(versionId: string): Promise<AjaxResult<FolderInfoVO>> {
  return request({
    url: `/api/folders/version/${versionId}/standard`,
    method: 'get'
  })
}
