/**
 * 统一错误处理工具
 * 
 * 提供统一的错误处理机制，包括：
 * - API错误处理
 * - 用户友好的错误消息
 * - 错误日志记录
 * - 错误重试机制
 * 
 * <AUTHOR>
 * @date 2025-07-08
 */

import { ElMessage, ElMessageBox } from 'element-plus'

/** 错误类型枚举 */
export enum ErrorType {
  NETWORK = 'NETWORK',
  API = 'API',
  VALIDATION = 'VALIDATION',
  PERMISSION = 'PERMISSION',
  BUSINESS = 'BUSINESS',
  UNKNOWN = 'UNKNOWN'
}

/** 错误信息接口 */
export interface ErrorInfo {
  type: ErrorType
  code?: string | number
  message: string
  details?: any
  timestamp: Date
  action?: string
}

/** 错误处理选项 */
export interface ErrorHandlerOptions {
  showMessage?: boolean
  logError?: boolean
  retryable?: boolean
  onRetry?: () => Promise<any>
  customMessage?: string
}

/**
 * 统一错误处理类
 */
export class ErrorHandler {
  /**
   * 处理API错误
   */
  static handleApiError(error: any, options: ErrorHandlerOptions = {}): ErrorInfo {
    const {
      showMessage = true,
      logError = true,
      customMessage
    } = options

    let errorInfo: ErrorInfo

    if (error.response) {
      // HTTP错误响应
      const { status, data } = error.response
      errorInfo = {
        type: ErrorHandler.getErrorTypeByStatus(status),
        code: status,
        message: customMessage || data?.msg || data?.message || ErrorHandler.getDefaultMessage(status),
        details: data,
        timestamp: new Date(),
        action: error.config?.url
      }
    } else if (error.request) {
      // 网络错误
      errorInfo = {
        type: ErrorType.NETWORK,
        message: customMessage || '网络连接失败，请检查网络设置',
        details: error.request,
        timestamp: new Date()
      }
    } else {
      // 其他错误
      errorInfo = {
        type: ErrorType.UNKNOWN,
        message: customMessage || error.message || '发生未知错误',
        details: error,
        timestamp: new Date()
      }
    }

    if (logError) {
      ErrorHandler.logError(errorInfo)
    }

    if (showMessage) {
      ErrorHandler.showErrorMessage(errorInfo)
    }

    return errorInfo
  }

  /**
   * 处理业务错误
   */
  static handleBusinessError(message: string, details?: any): ErrorInfo {
    const errorInfo: ErrorInfo = {
      type: ErrorType.BUSINESS,
      message,
      details,
      timestamp: new Date()
    }

    ErrorHandler.logError(errorInfo)
    ErrorHandler.showErrorMessage(errorInfo)

    return errorInfo
  }

  /**
   * 处理验证错误
   */
  static handleValidationError(message: string, field?: string): ErrorInfo {
    const errorInfo: ErrorInfo = {
      type: ErrorType.VALIDATION,
      message,
      details: { field },
      timestamp: new Date()
    }

    ElMessage.warning(message)
    ErrorHandler.logError(errorInfo)

    return errorInfo
  }

  /**
   * 显示错误消息
   */
  private static showErrorMessage(errorInfo: ErrorInfo): void {
    switch (errorInfo.type) {
      case ErrorType.NETWORK:
        ElMessage.error({
          message: errorInfo.message,
          duration: 5000,
          showClose: true
        })
        break
      case ErrorType.PERMISSION:
        ElMessage.warning({
          message: errorInfo.message,
          duration: 3000
        })
        break
      case ErrorType.VALIDATION:
        ElMessage.warning(errorInfo.message)
        break
      default:
        ElMessage.error({
          message: errorInfo.message,
          duration: 4000,
          showClose: true
        })
    }
  }

  /**
   * 记录错误日志
   */
  private static logError(errorInfo: ErrorInfo): void {
    const logLevel = errorInfo.type === ErrorType.VALIDATION ? 'warn' : 'error'
    
    console[logLevel]('🚨 错误处理:', {
      type: errorInfo.type,
      code: errorInfo.code,
      message: errorInfo.message,
      action: errorInfo.action,
      timestamp: errorInfo.timestamp.toISOString(),
      details: errorInfo.details
    })
  }

  /**
   * 根据HTTP状态码获取错误类型
   */
  private static getErrorTypeByStatus(status: number): ErrorType {
    if (status >= 400 && status < 500) {
      if (status === 401 || status === 403) {
        return ErrorType.PERMISSION
      }
      return ErrorType.VALIDATION
    } else if (status >= 500) {
      return ErrorType.API
    }
    return ErrorType.UNKNOWN
  }

  /**
   * 根据HTTP状态码获取默认错误消息
   */
  private static getDefaultMessage(status: number): string {
    const messages: Record<number, string> = {
      400: '请求参数错误',
      401: '未授权，请重新登录',
      403: '权限不足，无法访问',
      404: '请求的资源不存在',
      408: '请求超时',
      429: '请求过于频繁，请稍后再试',
      500: '服务器内部错误',
      502: '网关错误',
      503: '服务暂时不可用',
      504: '网关超时'
    }

    return messages[status] || `请求失败 (${status})`
  }

  /**
   * 创建重试机制
   */
  static createRetryHandler(
    operation: () => Promise<any>,
    maxRetries: number = 3,
    delay: number = 1000
  ) {
    return async (): Promise<any> => {
      let lastError: any

      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          return await operation()
        } catch (error) {
          lastError = error
          
          if (attempt === maxRetries) {
            throw error
          }

          console.warn(`🔄 操作失败，第 ${attempt} 次重试 (共 ${maxRetries} 次)`)
          await new Promise(resolve => setTimeout(resolve, delay * attempt))
        }
      }

      throw lastError
    }
  }

  /**
   * 显示确认重试对话框
   */
  static async showRetryDialog(message: string, operation: () => Promise<any>): Promise<any> {
    try {
      await ElMessageBox.confirm(
        `${message}\n\n是否重试？`,
        '操作失败',
        {
          confirmButtonText: '重试',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      return await operation()
    } catch (error) {
      if (error === 'cancel') {
        return null
      }
      throw error
    }
  }
}

/**
 * API调用包装器，自动处理错误
 */
export function withErrorHandler<T>(
  apiCall: () => Promise<T>,
  options: ErrorHandlerOptions = {}
): Promise<T> {
  return apiCall().catch(error => {
    ErrorHandler.handleApiError(error, options)
    throw error
  })
}

/**
 * 批量任务相关的错误处理
 */
export class BatchTaskErrorHandler extends ErrorHandler {
  /**
   * 处理任务创建错误
   */
  static handleTaskCreationError(error: any): ErrorInfo {
    return ErrorHandler.handleApiError(error, {
      customMessage: '创建批量任务失败，请检查参数后重试'
    })
  }

  /**
   * 处理任务列表获取错误
   */
  static handleTaskListError(error: any): ErrorInfo {
    return ErrorHandler.handleApiError(error, {
      customMessage: '获取任务列表失败，请刷新页面重试'
    })
  }

  /**
   * 处理任务删除错误
   */
  static handleTaskDeletionError(error: any): ErrorInfo {
    return ErrorHandler.handleApiError(error, {
      customMessage: '删除任务失败，请稍后重试'
    })
  }

  /**
   * 处理文件上传错误
   */
  static handleUploadError(error: any, fileName?: string): ErrorInfo {
    const message = fileName 
      ? `文件 "${fileName}" 上传失败`
      : '文件上传失败'

    return ErrorHandler.handleApiError(error, {
      customMessage: message
    })
  }
}

// 导出默认实例
export default ErrorHandler
