# 文件夹关联逻辑重构方案

## 概述

重构任务工作台的文件夹关联逻辑，实现版本重复校验、结构化关联信息管理，以及清晰的修改操作分化。

## 核心逻辑

1. **版本重复判断**：以 versionCode 为唯一标识，提交前校验，重复时提示关联现有版本
2. **修改逻辑分化**：
   - 新建版本关联：引导用户到版本管理页面修改版本信息
   - 已有版本关联：直接支持重新关联操作
3. **数据结构优化**：使用 AssociationVersionInfo 聚合关联信息

## 修改步骤

### 第一步：后端数据结构重构

#### 1.1 修改 FolderInfo.java

**目标**：重构关联信息的数据结构

**修改内容**：
- 创建静态内部类 `AssociationVersionInfo`
- 移除独立的关联字段，使用聚合对象
- 添加便捷判断方法

**AssociationVersionInfo 字段**：
```java
@Data
public static class AssociationVersionInfo {
    /** 关联的版本ID */
    private String versionId;
    
    /** 关联的版本编码 */
    private String versionCode;
    
    /** 关联类型：CREATED_NEW（新建版本关联）、LINKED_EXISTING（关联已有版本） */
    private String associationType;
    
    /** 关联操作的用户ID */
    private Long userId;
    
    /** 用户名 */
    private String userName;
    
    /** 部门ID */
    private Long deptId;
    
    /** 部门名称 */
    private String deptName;
    
    /** 首次关联时间 */
    private Date associationTime;
    
    /** 最后修改人ID */
    private Long lastModifiedBy;
    
    /** 最后修改人姓名 */
    private String lastModifiedByName;
    
    /** 最后修改时间 */
    private Date lastModifiedTime;
}
```

**便捷方法**：
```java
/** 判断是否已关联 */
public boolean isAssociated() {
    return associationInfo != null;
}

/** 判断是否为新建版本关联 */
public boolean isCreatedNewVersion() {
    return isAssociated() && "CREATED_NEW".equals(associationInfo.getAssociationType());
}
```

### 第二步：版本重复校验接口

#### 2.1 新增版本校验 API

**目标**：提供版本号重复校验功能

**新增接口**：
- `GET /api/versions/check-duplicate?versionCode={versionCode}`
- 返回是否重复及现有版本信息

**Service 方法**：
```java
/** 根据版本号查找版本 */
CertVersion findByVersionCode(String versionCode);
```

### 第三步：关联管理接口重构

#### 3.1 重构关联相关 API

**修改现有接口**：
- `associateFolderToVersion`：增加 associationType 参数

**新增接口**：
- `PUT /api/folders/{folderId}/reassociate`：重新关联
- `DELETE /api/folders/{folderId}/association`：取消关联

**修改查询接口**：
- 返回所有文件夹（不再按 status 筛选）
- 包含完整的 associationInfo 信息

### 第四步：前端版本创建组件修改

#### 4.1 SimpleVersionForm.vue 重复校验

**目标**：添加版本重复校验功能

**修改内容**：
- 提交前调用校验接口
- 重复时显示选择对话框
- 处理用户选择（关联现有/修改版本号/取消）

**用户选择处理**：
- 关联到现有版本：associationType 为 "LINKED_EXISTING"
- 创建新版本：associationType 为 "CREATED_NEW"

### 第五步：文件夹列表组件重构

#### 5.1 UnassignedFolderList.vue 显示逻辑

**目标**：支持显示所有文件夹并区分关联状态

**显示逻辑**：

**未关联文件夹**：
- 显示单选按钮和"比对"按钮

**已关联文件夹**：
- 显示关联信息：版本号、关联人、关联时间
- 根据关联类型显示操作按钮：
  - **新建版本关联 (CREATED_NEW)**：
    - "编辑版本"按钮 → `edit-version-clicked` 事件
    - "取消关联"按钮 → `cancel-association-clicked` 事件
  - **已有版本关联 (LINKED_EXISTING)**：
    - "重新关联"按钮 → `reassociate-clicked` 事件
    - "取消关联"按钮 → `cancel-association-clicked` 事件

**新增事件**：
```typescript
const emit = defineEmits<{
  'selection-change': [folders: FolderInfoVO[]]
  'folder-selected': [folder: FolderInfoVO]
  'compare-clicked': [folder: FolderInfoVO]
  'edit-version-clicked': [folder: FolderInfoVO]      // 新增
  'reassociate-clicked': [folder: FolderInfoVO]       // 新增  
  'cancel-association-clicked': [folder: FolderInfoVO] // 新增
}>()
```

### 第六步：主视图事件处理

#### 6.1 TaskWorkspaceView.vue 事件处理

**目标**：处理新的文件夹关联操作事件

**事件处理方法**：

**编辑版本处理**：
```typescript
const handleEditVersionClicked = (folder: FolderInfoVO) => {
  ElMessageBox.confirm(
    '版本信息修改需要在版本管理页面进行，是否跳转到版本管理？',
    '提示',
    {
      confirmButtonText: '跳转到版本管理',
      cancelButtonText: '取消',
      type: 'info'
    }
  ).then(() => {
    router.push(`/cert/version?versionId=${folder.associationInfo.versionId}`)
  })
}
```

**重新关联处理**：
- 复用现有的比对功能
- 修改关联逻辑为重新关联

**取消关联处理**：
- 确认对话框
- 调用取消关联 API
- 刷新组件数据

### 第七步：前端 API 接口补充

#### 7.1 补充 API 接口定义

**新增接口**：
```typescript
/** 校验版本号是否重复 */
export function checkVersionCodeDuplicate(versionCode: string)

/** 重新关联文件夹到其他版本 */
export function reassociateFolder(folderId: string, newVersionId: string)

/** 取消文件夹关联 */
export function cancelFolderAssociation(folderId: string)
```

**修改现有接口**：
```typescript
/** 关联文件夹到版本 */
export function associateFolderToVersion(
  folderId: string, 
  versionId: string, 
  associationType: 'CREATED_NEW' | 'LINKED_EXISTING'
)
```

## 实现优先级

1. **第一阶段**：数据结构重构（步骤1-3）
2. **第二阶段**：版本重复校验（步骤4）  
3. **第三阶段**：前端界面适配（步骤5-6）
4. **第四阶段**：API 接口补充和联调（步骤7）

## 预期效果

- ✅ 版本号唯一性校验，避免重复创建
- ✅ 新建版本关联引导到版本管理页面
- ✅ 已有版本关联支持直接重新关联
- ✅ 清晰的数据结构和操作逻辑分离
- ✅ 结构化的关联信息管理
- ✅ 可维护、可扩展的代码架构
