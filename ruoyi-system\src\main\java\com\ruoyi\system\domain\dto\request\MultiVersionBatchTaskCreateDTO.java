package com.ruoyi.system.domain.dto.request;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 多版本批量任务创建DTO
 * 
 * 用于一次性创建包含多个文件夹版本的批量上传任务。
 * 每个版本代表一个独立的文件夹，包含自己的元数据和文件列表。
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
public class MultiVersionBatchTaskCreateDTO {

    /**
     * 主任务名称（可选，如果不提供将由后端自动生成）
     * 格式：时间_部门名称_批量上传任务
     */
    @Size(max = 200, message = "任务名称长度不能超过200个字符")
    private String taskName;

    /**
     * 任务描述
     */
    @Size(max = 500, message = "任务描述长度不能超过500个字符")
    private String description;

    /**
     * 部门ID
     */
    @NotNull(message = "部门ID不能为空")
    private Long deptId;

    /**
     * 创建者用户名
     */
    @NotBlank(message = "创建者不能为空")
    private String createdBy;

    /**
     * 版本列表
     * 每个版本代表一个文件夹及其元数据
     */
    @NotEmpty(message = "版本列表不能为空")
    @Valid
    private List<VersionInfo> versions;

    /**
     * 版本信息
     */
    @Data
    public static class VersionInfo {
        /**
         * 文件夹名称
         */
        @NotBlank(message = "文件夹名称不能为空")
        private String folderName;

        /**
         * 文件夹路径
         */
        @NotBlank(message = "文件夹路径不能为空")
        private String folderPath;

        /**
         * 国家ID
         */
        @NotNull(message = "国家ID不能为空")
        private Long countryId;

        /**
         * 证件类型ID
         */
        @NotNull(message = "证件类型ID不能为空")
        private Long certTypeId;

        /**
         * 签发年份
         */
        @NotBlank(message = "签发年份不能为空")
        private String issueYear;

        /**
         * 签发地（可选）
         */
        private String issuePlace;

        /**
         * 证件号前缀（可选）
         */
        private String certNumberPrefix;

        /**
         * 文件数量
         */
        @NotNull(message = "文件数量不能为空")
        private Integer fileCount;

        /**
         * 文件列表（可选，用于预创建ImageRepository记录）
         */
        private List<FileInfo> files;

        /**
         * 版本索引（用于排序）
         */
        private Integer versionIndex;
    }

    /**
     * 文件信息
     */
    @Data
    public static class FileInfo {
        /**
         * 文件名
         */
        @NotBlank(message = "文件名不能为空")
        private String fileName;

        /**
         * 相对路径
         */
        private String relativePath;

        /**
         * 文件大小
         */
        private Long fileSize;

        /**
         * 文件类型
         */
        private String fileType;

        /**
         * 最后修改时间
         */
        private Long lastModified;
    }

    /**
     * 获取版本数量
     */
    public int getVersionCount() {
        return versions != null ? versions.size() : 0;
    }

    /**
     * 获取总文件数量
     */
    public int getTotalFileCount() {
        if (versions == null) {
            return 0;
        }
        return versions.stream()
                .mapToInt(v -> v.getFileCount() != null ? v.getFileCount() : 0)
                .sum();
    }

    /**
     * 验证数据完整性
     */
    public boolean isValid() {
        if (versions == null || versions.isEmpty()) {
            return false;
        }

        for (VersionInfo version : versions) {
            if (version.getCountryId() == null || 
                version.getCertTypeId() == null || 
                version.getFileCount() == null || 
                version.getFileCount() <= 0) {
                return false;
            }

            if (version.getFolderName() == null || version.getFolderName().trim().isEmpty()) {
                return false;
            }
        }

        return true;
    }

    /**
     * 设置版本索引
     */
    public void setVersionIndexes() {
        if (versions != null) {
            for (int i = 0; i < versions.size(); i++) {
                versions.get(i).setVersionIndex(i);
            }
        }
    }
}
