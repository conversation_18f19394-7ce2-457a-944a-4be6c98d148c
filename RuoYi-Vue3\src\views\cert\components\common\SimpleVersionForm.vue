<template>
  <el-dialog
    v-model="dialogVisible"
    title="创建新版本"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      label-position="left"
    >
      <!-- 文件夹信息展示 -->
      <div class="folder-info-section">
        <h4>选中的文件夹</h4>
        <el-tag type="primary" size="large">
          {{ folderInfo?.folderName || '未知文件夹' }}
        </el-tag>
      </div>

      <!-- 版本号输入 -->
      <el-form-item label="版本号" prop="versionCode">
        <el-input
          v-model="formData.versionCode"
          placeholder="请输入版本号"
          @input="handleVersionCodeChange"
        />
        <div class="version-format-tip">
          <el-text type="info" size="small">
            格式：国家代码_证件类型代码_年份_其他信息 (如：GBR_13_2004_XDD85)
          </el-text>
        </div>
        <div v-if="versionValidation.message" class="validation-message">
          <el-text :type="versionValidation.isValid ? 'success' : 'danger'" size="small">
            {{ versionValidation.message }}
          </el-text>
        </div>
      </el-form-item>

      <!-- 国家选择 -->
      <el-form-item label="国家" prop="countryCode">
        <CountrySelect
          v-model="formData.countryCode"
          @change="handleCountryChange"
          width="100%"
        />
      </el-form-item>

      <!-- 证件类型选择 -->
      <el-form-item label="证件类型" prop="certTypeCode">
        <CertTypeSelect
          v-model="formData.certTypeCode"
          @change="handleCertTypeChange"
          width="100%"
        />
      </el-form-item>

      <!-- 版本描述 -->
      <el-form-item label="版本描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入版本描述（可选）"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="submitting"
          :disabled="!versionValidation.isValid"
          @click="handleSubmit"
        >
          创建版本
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import CountrySelect from '@/components/CertCommon/CountrySelect.vue'
import CertTypeSelect from '@/components/CertCommon/CertTypeSelect.vue'
import { createVersionFromFolder } from '@/api/cert/version'
import type { FolderInfoVO } from '@/api/cert/folder'

// Props 定义
interface Props {
  visible: boolean
  folderInfo: FolderInfoVO | null
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  folderInfo: null
})

// Emits 定义
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'success': []
  'close': []
}>()

// 响应式数据
const formRef = ref()
const submitting = ref(false)

// 弹窗显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 表单数据
const formData = reactive({
  versionCode: '',
  countryCode: '',
  certTypeCode: '',
  description: ''
})

// 表单验证规则
const formRules = {
  versionCode: [
    { required: true, message: '请输入版本号', trigger: 'blur' },
    { validator: validateVersionCode, trigger: 'blur' }
  ],
  countryCode: [
    { required: true, message: '请选择国家', trigger: 'change' }
  ],
  certTypeCode: [
    { required: true, message: '请选择证件类型', trigger: 'change' }
  ]
}

// 版本号校验状态
const versionValidation = reactive({
  isValid: false,
  message: ''
})

// 版本号格式校验函数
function validateVersionFormat(versionCode: string): { isValid: boolean; message: string } {
  if (!versionCode || versionCode.trim() === '') {
    return { isValid: false, message: '版本号不能为空' }
  }

  const parts = versionCode.split('_')
  if (parts.length < 3) {
    return { isValid: false, message: '版本号格式错误，至少需要包含：国家代码_证件类型代码_年份' }
  }

  // 校验国家代码（2-3位大写字母）
  const countryCode = parts[0]
  if (!/^[A-Z]{2,3}$/.test(countryCode)) {
    return { isValid: false, message: '国家代码格式错误，应为2-3位大写字母' }
  }

  // 校验证件类型代码（数字或字母数字组合）
  const certTypeCode = parts[1]
  if (!/^[A-Z0-9]+$/.test(certTypeCode)) {
    return { isValid: false, message: '证件类型代码格式错误，应为数字或字母数字组合' }
  }

  // 校验年份（4位数字）
  const year = parts[2]
  if (!/^\d{4}$/.test(year)) {
    return { isValid: false, message: '年份格式错误，应为4位数字' }
  }

  return { isValid: true, message: '版本号格式正确' }
}

// 表单验证器
function validateVersionCode(rule: any, value: string, callback: Function) {
  const validation = validateVersionFormat(value)
  if (validation.isValid) {
    callback()
  } else {
    callback(new Error(validation.message))
  }
}

// 版本号变化处理
function handleVersionCodeChange() {
  const validation = validateVersionFormat(formData.versionCode)
  versionValidation.isValid = validation.isValid
  versionValidation.message = validation.message
}

// 更新版本号的指定部分
function updateVersionCodePart(partIndex: number, newValue: string) {
  const parts = formData.versionCode.split('_')
  if (parts.length >= partIndex + 1) {
    parts[partIndex] = newValue
    formData.versionCode = parts.join('_')
    handleVersionCodeChange()
  }
}

// 国家选择变化处理
function handleCountryChange(countryCode: string) {
  if (countryCode && formData.versionCode) {
    updateVersionCodePart(0, countryCode)
  }
}

// 证件类型选择变化处理
function handleCertTypeChange(certTypeCode: string) {
  if (certTypeCode && formData.versionCode) {
    updateVersionCodePart(1, certTypeCode)
  }
}

// 初始化表单数据
function initializeForm() {
  if (props.folderInfo?.preParseVersionInfo) {
    const parseInfo = props.folderInfo.preParseVersionInfo
    
    // 预填充版本号
    if (parseInfo.parsedVersionCode) {
      formData.versionCode = parseInfo.parsedVersionCode
    }
    
    // 预填充国家代码
    if (parseInfo.countryCode) {
      formData.countryCode = parseInfo.countryCode
    }
    
    // 预填充证件类型代码
    if (parseInfo.certTypeCode) {
      formData.certTypeCode = parseInfo.certTypeCode
    }
    
    // 初始校验
    nextTick(() => {
      handleVersionCodeChange()
    })
  }
}

// 监听 folderInfo 变化
watch(() => props.folderInfo, (newFolderInfo) => {
  if (newFolderInfo) {
    initializeForm()
  }
}, { immediate: true })

// 监听弹窗显示状态
watch(() => props.visible, (visible) => {
  if (visible) {
    // 重置表单
    formData.versionCode = ''
    formData.countryCode = ''
    formData.certTypeCode = ''
    formData.description = ''
    versionValidation.isValid = false
    versionValidation.message = ''
    
    // 初始化表单数据
    nextTick(() => {
      initializeForm()
    })
  }
})

// 提交处理
async function handleSubmit() {
  try {
    // 表单验证
    await formRef.value?.validate()
    
    if (!versionValidation.isValid) {
      ElMessage.error('版本号格式不正确，请检查后重试')
      return
    }
    
    submitting.value = true
    
    // 构造请求数据
    const requestData = {
      folderId: props.folderInfo?.folderId,
      versionCode: formData.versionCode.trim(),
      description: formData.description.trim() || `基于文件夹 "${props.folderInfo?.folderName}" 创建的版本`
    }
    
    console.log('创建版本请求数据:', requestData)
    
    // 调用API
    const result = await createVersionFromFolder(requestData)
    
    if (result.code === 200) {
      ElMessage.success(`版本 "${formData.versionCode}" 创建成功`)
      emit('success')
      handleClose()
    } else {
      ElMessage.error(result.msg || '创建版本失败')
    }
    
  } catch (error) {
    console.error('创建版本失败:', error)
    ElMessage.error('创建版本失败: ' + (error.message || '未知错误'))
  } finally {
    submitting.value = false
  }
}

// 关闭处理
function handleClose() {
  emit('close')
  emit('update:visible', false)
}
</script>

<style scoped>
.folder-info-section {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 6px;
}

.folder-info-section h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.version-format-tip {
  margin-top: 4px;
}

.validation-message {
  margin-top: 4px;
}

.dialog-footer {
  text-align: right;
}
</style>
