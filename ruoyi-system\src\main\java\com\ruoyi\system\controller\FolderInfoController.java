package com.ruoyi.system.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.dto.request.FolderQueryDTO;
import com.ruoyi.system.domain.dto.request.FolderUpdateDTO;
import com.ruoyi.system.domain.dto.request.ReviewActionDTO;

import com.ruoyi.system.domain.dto.request.VersionAssociationDTO;
import com.ruoyi.system.domain.dto.request.StandardSampleRequestDTO;
import com.ruoyi.system.domain.dto.response.FolderInfoVO;
import com.ruoyi.system.domain.dto.response.StandardSampleInfoVO;
import com.ruoyi.system.domain.mongo.FolderInfo;
import com.ruoyi.system.service.IFolderInfoService;
import com.ruoyi.system.service.IFolderInfoService.StandardSampleInfo;

import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 文件夹信息管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@RestController
@RequestMapping("/api/folders")
@Validated
public class FolderInfoController extends BaseController {
    
    private static final Logger log = LoggerFactory.getLogger(FolderInfoController.class);
    
    @Autowired
    private IFolderInfoService folderInfoService;
    
    /**
     * 修复文件夹状态
     * POST /api/folders/{folderId}/fix-status
     *
     * 用于修复已完成上传但状态仍为PROCESSING的文件夹
     */
    @PreAuthorize("@ss.hasPermi('cert:folder:edit')")
    @Log(title = "文件夹状态修复", businessType = BusinessType.UPDATE)
    @PostMapping("/{folderId}/fix-status")
    public AjaxResult fixFolderStatus(@PathVariable String folderId) {
        try {
            log.info("开始修复文件夹状态，folderId: {}", folderId);

            boolean success = folderInfoService.fixFolderStatus(folderId);

            if (success) {
                log.info("文件夹状态修复成功，folderId: {}", folderId);
                return success("文件夹状态修复成功");
            } else {
                log.warn("文件夹状态无需修复或修复失败，folderId: {}", folderId);
                return error("文件夹状态无需修复或修复失败");
            }

        } catch (Exception e) {
            log.error("修复文件夹状态失败，folderId: {}", folderId, e);
            return error("修复文件夹状态失败: " + e.getMessage());
        }
    }

    /**
     * 继续上传文件夹
     * POST /api/folders/{folderId}/resume-upload
     *
     * 用于继续上传中断的文件夹
     */
    @PreAuthorize("@ss.hasPermi('cert:folder:edit')")
    @Log(title = "继续上传", businessType = BusinessType.UPDATE)
    @PostMapping("/{folderId}/resume-upload")
    public AjaxResult resumeUpload(@PathVariable String folderId) {
        try {
            log.info("开始继续上传，folderId: {}", folderId);

            Map<String, Object> result = folderInfoService.prepareResumeUpload(folderId);

            log.info("继续上传准备完成，folderId: {}", folderId);
            return success(result);

        } catch (Exception e) {
            log.error("继续上传准备失败，folderId: {}", folderId, e);
            return error("继续上传准备失败: " + e.getMessage());
        }
    }

    /**
     * 关联文件夹到已有版本
     * POST /api/folders/{folderId}/associate
     */
    @PreAuthorize("@ss.hasPermi('cert:folder:edit')")
    @Log(title = "文件夹管理", businessType = BusinessType.UPDATE)
    @PostMapping("/{folderId}/associate")
    public AjaxResult associateToVersion(
            @PathVariable String folderId,
            @Valid @RequestBody VersionAssociationDTO dto) {
        try {
            log.info("开始关联文件夹[{}]到版本[{}]", folderId, dto.getVersionId());
            boolean success = folderInfoService.associateFolderToVersion(folderId, dto.getVersionId());
            if (success) {
                return AjaxResult.success("关联文件夹到版本成功");
            } else {
                return AjaxResult.error("关联文件夹到版本失败");
            }
        } catch (Exception e) {
            log.error("关联文件夹到版本失败: {}", e.getMessage(), e);
            return AjaxResult.error("关联文件夹到版本失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询文件夹列表
     * GET /api/folders
     */
    @PreAuthorize("@ss.hasPermi('cert:folder:list')")
    @GetMapping
    public TableDataInfo listFolders(FolderQueryDTO query) {
        try {
            log.info("查询文件夹列表: {}", query);
            startPage();
            
            List<FolderInfoVO> folders = queryFoldersByConditions(query);
            TableDataInfo result = getDataTable(folders);
            log.info("c，返回数据: 总记录数={}, 当前页数据量={}",
                    result.getTotal(), folders.size());
            return result;
        } catch (Exception e) {
            log.error("查询文件夹列表失败: {}", e.getMessage(), e);
            return new TableDataInfo();
        }
    }
    
    /**
     * 获取文件夹详情
     * GET /api/folders/{folderId}
     */
    @PreAuthorize("@ss.hasPermi('cert:folder:query')")
    @GetMapping("/{folderId}")
    public AjaxResult getFolderDetails(@PathVariable String folderId) {
        try {
            log.info("获取文件夹详情: {}", folderId);
            FolderInfoVO folder = folderInfoService.getFolderById(folderId);
            if (folder != null) {
                return AjaxResult.success("获取文件夹详情成功", folder);
            } else {
                return AjaxResult.error("文件夹不存在");
            }
        } catch (Exception e) {
            log.error("获取文件夹详情失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取文件夹详情失败: " + e.getMessage());
        }
    }

    /**
     * 更新文件夹信息
     * PUT /api/folders/{folderId}
     */
    @PreAuthorize("@ss.hasPermi('cert:folder:edit')")
    @Log(title = "文件夹管理", businessType = BusinessType.UPDATE)
    @PutMapping("/{folderId}")
    public AjaxResult updateFolderInfo(@PathVariable String folderId,
                                      @Valid @RequestBody FolderUpdateDTO updateDTO) {
        try {
            log.info("更新文件夹信息: folderId={}, updateData={}", folderId, updateDTO);

            // 参数验证
            if (folderId == null || folderId.trim().isEmpty()) {
                return AjaxResult.error("文件夹ID不能为空");
            }

            boolean success = folderInfoService.updateFolderInfo(folderId, updateDTO);
            if (success) {
                return AjaxResult.success("更新文件夹信息成功");
            } else {
                return AjaxResult.error("更新文件夹信息失败");
            }
        } catch (Exception e) {
            log.error("更新文件夹信息失败: {}", e.getMessage(), e);
            return AjaxResult.error("更新文件夹信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取文件夹统计信息
     * GET /api/folders/{folderId}/statistics
     */
    @PreAuthorize("@ss.hasPermi('cert:folder:query')")
    @GetMapping("/{folderId}/statistics")
    public AjaxResult getFolderStatistics(@PathVariable String folderId) {
        try {
            log.info("获取文件夹统计信息: {}", folderId);
            Map<String, Object> statistics = folderInfoService.getFolderStatistics(folderId);
            return AjaxResult.success("获取文件夹统计信息成功", statistics);
        } catch (Exception e) {
            log.error("获取文件夹统计信息失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取文件夹统计信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取任务的文件夹统计
     * GET /api/folders/task/{taskId}/statistics
     */
    @PreAuthorize("@ss.hasPermi('cert:folder:query')")
    @GetMapping("/task/{taskId}/statistics")
    public AjaxResult getTaskFolderStatistics(@PathVariable String taskId) {
        try {
            log.info("获取任务[{}]的文件夹统计", taskId);
            Map<String, Object> statistics = folderInfoService.getTaskFolderStatistics(taskId);
            return AjaxResult.success("获取任务文件夹统计成功", statistics);
        } catch (Exception e) {
            log.error("获取任务文件夹统计失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取任务文件夹统计失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除文件夹
     * DELETE /api/folders/{folderId}
     */
    @PreAuthorize("@ss.hasPermi('cert:folder:remove')")
    @Log(title = "文件夹管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{folderId}")
    public AjaxResult deleteFolder(@PathVariable String folderId) {
        try {
            log.info("删除文件夹: {}", folderId);
            boolean success = folderInfoService.deleteFolder(folderId);
            if (success) {
                return AjaxResult.success("删除文件夹成功");
            } else {
                return AjaxResult.error("删除文件夹失败");
            }
        } catch (Exception e) {
            log.error("删除文件夹失败: {}", e.getMessage(), e);
            return AjaxResult.error("删除文件夹失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据查询条件获取文件夹列表
     */
    private List<FolderInfoVO> queryFoldersByConditions(FolderQueryDTO query) {
        // 检查是否有有效的查询条件（非空且非空字符串）
        boolean hasValidConditions = false;

        // 检查各个查询条件
        if (isNotEmpty(query.getTaskId())) {
            hasValidConditions = true;
            return folderInfoService.getFoldersByTaskId(query.getTaskId());
        }

        if (isNotEmpty(query.getVersionId())) {
            hasValidConditions = true;
            return folderInfoService.getFoldersByVersionId(query.getVersionId());
        }

        if (isNotEmpty(query.getStatus())) {
            hasValidConditions = true;
            return folderInfoService.getFoldersByStatus(query.getStatus());
        }

        if (isNotEmpty(query.getFolderType())) {
            hasValidConditions = true;
            return folderInfoService.getFoldersByType(query.getFolderType());
        }

        if (query.getCountryId() != null) {
            hasValidConditions = true;
            return folderInfoService.getFoldersByCountryId(query.getCountryId());
        }

        if (isNotEmpty(query.getCountryCode())) {
            hasValidConditions = true;
            return folderInfoService.getFoldersByCountryCode(query.getCountryCode());
        }

        if (query.getCertTypeId() != null) {
            hasValidConditions = true;
            return folderInfoService.getFoldersByCertTypeId(query.getCertTypeId());
        }

        if (isNotEmpty(query.getCertTypeCode())) {
            hasValidConditions = true;
            return folderInfoService.getFoldersByCertTypeCode(query.getCertTypeCode());
        }

        if (isNotEmpty(query.getIssueYear())) {
            hasValidConditions = true;
            return folderInfoService.getFoldersByIssueYear(query.getIssueYear());
        }

        if (query.getDeptId() != null) {
            hasValidConditions = true;
            return folderInfoService.getFoldersByDeptId(query.getDeptId());
        }

        if (query.getUploaderId() != null) {
            hasValidConditions = true;
            return folderInfoService.getFoldersByUploaderId(query.getUploaderId());
        }

        if (isNotEmpty(query.getKeyword())) {
            hasValidConditions = true;
            return folderInfoService.getFoldersByKeyword(query.getKeyword());
        }

        // 如果没有特定的筛选条件，返回所有文件夹（用于初始加载和重置查询）
        log.info("未提供特定查询条件，返回所有文件夹列表");
        return folderInfoService.getAllFolders();
    }

    /**
     * 检查字符串是否不为空且不为空字符串
     */
    private boolean isNotEmpty(String str) {
        return str != null && !str.trim().isEmpty();
    }

    // ==================== 版本审核和样本分类相关接口 ====================

    /**
     * 第三方版本检测
     * POST /api/folders/{folderId}/detect-version
     */
    @PreAuthorize("@ss.hasPermi('cert:folder:detect')")
    @Log(title = "文件夹管理", businessType = BusinessType.OTHER)
    @PostMapping("/{folderId}/detect-version")
    public AjaxResult detectVersionByThirdParty(@PathVariable String folderId) {
        try {
            log.info("开始对文件夹[{}]进行第三方版本检测", folderId);

            // 参数验证
            if (folderId == null || folderId.trim().isEmpty()) {
                return AjaxResult.error("文件夹ID不能为空");
            }

            FolderInfo.ThirdPartyDetectionResult result = folderInfoService.detectVersionByThirdParty(folderId);

            if ("success".equals(result.getDetectionStatus())) {
                return AjaxResult.success("第三方版本检测成功", result);
            } else {
                return AjaxResult.error("第三方版本检测失败: " + result.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("第三方版本检测失败: {}", e.getMessage(), e);
            return AjaxResult.error("第三方版本检测失败: " + e.getMessage());
        }
    }

    /**
     * 获取待审核文件夹列表
     * GET /api/folders/pending-review
     */
    @PreAuthorize("@ss.hasPermi('cert:folder:review')")
    @GetMapping("/pending-review")
    public TableDataInfo getPendingReviewFolders(FolderQueryDTO query) {
        try {
            log.info("查询待审核文件夹列表: {}", query);
            startPage();

            List<FolderInfo> folders = folderInfoService.getPendingReviewFolders(query);

            // 转换为VO对象
            List<FolderInfoVO> folderVOs = new ArrayList<>();
            for (FolderInfo folder : folders) {
                FolderInfoVO vo = folderInfoService.getFolderById(folder.getFolderId());
                if (vo != null) {
                    folderVOs.add(vo);
                }
            }

            TableDataInfo result = getDataTable(folderVOs);
            log.info("查询待审核文件夹完成，返回数据: 总记录数={}, 当前页数据量={}",
                    result.getTotal(), folderVOs.size());
            return result;

        } catch (Exception e) {
            log.error("查询待审核文件夹列表失败: {}", e.getMessage(), e);
            return new TableDataInfo();
        }
    }

    /**
     * 审核文件夹版本关联
     * POST /api/folders/{folderId}/review
     */
    @PreAuthorize("@ss.hasPermi('cert:folder:review')")
    @Log(title = "文件夹管理", businessType = BusinessType.UPDATE)
    @PostMapping("/{folderId}/review")
    public AjaxResult reviewFolderVersion(
            @PathVariable String folderId,
            @Valid @RequestBody ReviewActionDTO dto) {
        try {
            log.info("开始审核文件夹[{}]版本关联，操作: {}", folderId, dto.getAction());

            // 参数验证
            if (folderId == null || folderId.trim().isEmpty()) {
                return AjaxResult.error("文件夹ID不能为空");
            }

            if (dto.getAction() == null || dto.getAction().trim().isEmpty()) {
                return AjaxResult.error("审核操作不能为空");
            }

            if (!"approve".equals(dto.getAction()) && !"reject".equals(dto.getAction())) {
                return AjaxResult.error("无效的审核操作，只支持 approve 或 reject");
            }

            if (dto.getReviewerId() == null || dto.getReviewerId().trim().isEmpty()) {
                return AjaxResult.error("审核人ID不能为空");
            }

            boolean success = folderInfoService.reviewFolderVersion(folderId, dto);

            if (success) {
                String actionText = "approve".equals(dto.getAction()) ? "通过" : "驳回";
                return AjaxResult.success("文件夹版本关联审核" + actionText + "成功");
            } else {
                return AjaxResult.error("文件夹版本关联审核失败");
            }

        } catch (Exception e) {
            log.error("审核文件夹[{}]版本关联失败: {}", folderId, e.getMessage(), e);
            return AjaxResult.error("审核文件夹版本关联失败: " + e.getMessage());
        }
    }

    // ==================== 标准样本管理接口 ====================

    /**
     * 设置标准样本文件夹
     * POST /api/folders/{folderId}/set-standard
     */
    @PreAuthorize("@ss.hasPermi('cert:folder:standard:set')")
    @Log(title = "标准样本管理", businessType = BusinessType.UPDATE)
    @PostMapping("/{folderId}/set-standard")
    public AjaxResult setStandardFolder(@PathVariable String folderId,
                                       @Valid @RequestBody StandardSampleRequestDTO requestDTO) {
        try {
            log.info("设置标准样本文件夹: folderId={}, versionId={}", folderId, requestDTO.getVersionId());

            boolean success = folderInfoService.setStandardFolder(requestDTO.getVersionId(), folderId);

            if (success) {
                log.info("设置标准样本文件夹成功: folderId={}", folderId);
                return AjaxResult.success("设置标准样本文件夹成功");
            } else {
                log.warn("设置标准样本文件夹失败: folderId={}", folderId);
                return AjaxResult.error("设置标准样本文件夹失败");
            }

        } catch (Exception e) {
            log.error("设置标准样本文件夹失败: folderId={}, error={}", folderId, e.getMessage(), e);
            return AjaxResult.error("设置标准样本文件夹失败: " + e.getMessage());
        }
    }

    /**
     * 替换标准样本文件夹
     * POST /api/folders/{folderId}/replace-standard
     */
    @PreAuthorize("@ss.hasPermi('cert:folder:standard:replace')")
    @Log(title = "标准样本管理", businessType = BusinessType.UPDATE)
    @PostMapping("/{folderId}/replace-standard")
    public AjaxResult replaceStandardFolder(@PathVariable String folderId,
                                           @Valid @RequestBody StandardSampleRequestDTO requestDTO) {
        try {
            log.info("替换标准样本文件夹: folderId={}, versionId={}, keepAnnotations={}",
                    folderId, requestDTO.getVersionId(), requestDTO.getKeepAnnotations());

            boolean success = folderInfoService.replaceStandardFolder(
                    requestDTO.getVersionId(), folderId, requestDTO.getKeepAnnotations());

            if (success) {
                log.info("替换标准样本文件夹成功: folderId={}", folderId);
                return AjaxResult.success("替换标准样本文件夹成功");
            } else {
                log.warn("替换标准样本文件夹失败: folderId={}", folderId);
                return AjaxResult.error("替换标准样本文件夹失败");
            }

        } catch (Exception e) {
            log.error("替换标准样本文件夹失败: folderId={}, error={}", folderId, e.getMessage(), e);
            return AjaxResult.error("替换标准样本文件夹失败: " + e.getMessage());
        }
    }

    /**
     * 取消标准样本文件夹
     * POST /api/folders/{folderId}/remove-standard
     */
    @PreAuthorize("@ss.hasPermi('cert:folder:standard:remove')")
    @Log(title = "标准样本管理", businessType = BusinessType.UPDATE)
    @PostMapping("/{folderId}/remove-standard")
    public AjaxResult removeStandardFolder(@PathVariable String folderId,
                                          @Valid @RequestBody StandardSampleRequestDTO requestDTO) {
        try {
            log.info("取消标准样本文件夹: folderId={}, versionId={}", folderId, requestDTO.getVersionId());

            boolean success = folderInfoService.removeStandardFolder(requestDTO.getVersionId(), folderId);

            if (success) {
                log.info("取消标准样本文件夹成功: folderId={}", folderId);
                return AjaxResult.success("取消标准样本文件夹成功");
            } else {
                log.warn("取消标准样本文件夹失败: folderId={}", folderId);
                return AjaxResult.error("取消标准样本文件夹失败");
            }

        } catch (Exception e) {
            log.error("取消标准样本文件夹失败: folderId={}, error={}", folderId, e.getMessage(), e);
            return AjaxResult.error("取消标准样本文件夹失败: " + e.getMessage());
        }
    }

    /**
     * 获取文件夹标准样本信息
     * GET /api/folders/{folderId}/standard-info
     */
    @PreAuthorize("@ss.hasPermi('cert:folder:query')")
    @GetMapping("/{folderId}/standard-info")
    public AjaxResult getStandardSampleInfo(@PathVariable String folderId) {
        try {
            log.info("获取文件夹标准样本信息: folderId={}", folderId);

            StandardSampleInfo info = folderInfoService.getStandardSampleInfo(folderId);

            // 转换为VO对象
            StandardSampleInfoVO vo = convertToStandardSampleInfoVO(info, folderId);

            log.info("获取文件夹标准样本信息成功: folderId={}, isStandardSample={}",
                    folderId, vo.getIsStandardSample());
            return AjaxResult.success("获取标准样本信息成功", vo);

        } catch (Exception e) {
            log.error("获取文件夹标准样本信息失败: folderId={}, error={}", folderId, e.getMessage(), e);
            return AjaxResult.error("获取标准样本信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取版本的标准样本文件夹
     * GET /api/folders/version/{versionId}/standard
     */
    @PreAuthorize("@ss.hasPermi('cert:folder:query')")
    @GetMapping("/version/{versionId}/standard")
    public AjaxResult getVersionStandardFolder(@PathVariable String versionId) {
        try {
            log.info("获取版本标准样本文件夹: versionId={}", versionId);

            Optional<FolderInfo> standardFolder = folderInfoService.getStandardFolder(versionId);

            if (standardFolder.isPresent()) {
                FolderInfoVO folderVO = folderInfoService.getFolderById(standardFolder.get().getFolderId());
                log.info("获取版本标准样本文件夹成功: versionId={}, folderId={}",
                        versionId, standardFolder.get().getFolderId());
                return AjaxResult.success("获取版本标准样本文件夹成功", folderVO);
            } else {
                log.warn("版本没有标准样本文件夹: versionId={}", versionId);
                return AjaxResult.error("版本没有标准样本文件夹");
            }

        } catch (Exception e) {
            log.error("获取版本标准样本文件夹失败: versionId={}, error={}", versionId, e.getMessage(), e);
            return AjaxResult.error("获取版本标准样本文件夹失败: " + e.getMessage());
        }
    }

    /**
     * 转换标准样本信息为VO对象
     */
    private StandardSampleInfoVO convertToStandardSampleInfoVO(StandardSampleInfo info, String folderId) {
        StandardSampleInfoVO vo = new StandardSampleInfoVO();
        vo.setFolderId(folderId);
        vo.setVersionId(info.getVersionId());
        vo.setIsStandardSample(info.isStandardSample());
        vo.setCanSetAsStandard(info.isCanSetAsStandard());
        vo.setCanRemoveStandard(info.isCanRemoveStandard());
        vo.setReason(info.getReason());
        vo.setTemplateCount(info.getTemplateCount());

        // 获取文件夹详细信息
        try {
            FolderInfoVO folderInfo = folderInfoService.getFolderById(folderId);
            if (folderInfo != null) {
                vo.setFolderName(folderInfo.getFolderName());
                // 这里可以添加图片统计信息
                vo.setTotalImageCount(0); // 需要从统计服务获取
                vo.setAnnotatableImageCount(0); // 需要从统计服务获取
            }
        } catch (Exception e) {
            log.warn("获取文件夹详细信息失败: folderId={}, error={}", folderId, e.getMessage());
        }

        return vo;
    }

}
