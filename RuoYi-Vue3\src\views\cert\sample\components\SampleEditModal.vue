<template>
  <el-dialog
    :model-value="visible"
    title="编辑样本信息"
    width="800px"
    :close-on-click-modal="false"
    @update:model-value="handleVisibleChange"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="loading"
    >
      <!-- 基本信息 -->
      <el-card class="mb-4" shadow="never">
        <template #header>
          <span class="card-title">基本信息</span>
        </template>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="文件夹ID" prop="folderId">
              <el-input v-model="formData.folderId" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="文件夹名称" prop="folderName">
              <el-input v-model="formData.folderName" placeholder="请输入文件夹名称" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="样本类型" prop="folderType">
              <el-select v-model="formData.folderType" placeholder="请选择样本类型" style="width: 100%">
                <el-option label="标准样本" value="standard" />
                <el-option label="普通样本" value="regular" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="formData.status" placeholder="请选择状态" style="width: 100%">
                <el-option label="未关联" value="unassociated" />
                <el-option label="已关联" value="associated" />
                <el-option label="处理中" value="processing" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="审核状态" prop="reviewStatus">
              <el-select v-model="formData.reviewStatus" placeholder="请选择审核状态" style="width: 100%">
                <el-option label="待审核" value="pending" />
                <el-option label="审核通过" value="approved" />
                <el-option label="审核驳回" value="rejected" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="版本关联方式" prop="versionAssociationMethod">
              <el-select v-model="formData.versionAssociationMethod" placeholder="请选择关联方式" style="width: 100%">
                <el-option label="自动检测" value="auto_detect" />
                <el-option label="手动选择" value="manual_select" />
                <el-option label="第三方检测" value="third_party" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 证件信息 -->
      <el-card class="mb-4" shadow="never">
        <template #header>
          <span class="card-title">证件信息</span>
        </template>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="国家" prop="countryId">
              <el-select
                v-model="formData.countryId"
                placeholder="请选择国家"
                style="width: 100%"
                filterable
                @change="handleCountryChange"
              >
                <el-option
                  v-for="country in countryList"
                  :key="country.id"
                  :label="`${country.name} (${country.code})`"
                  :value="country.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="证件类型" prop="certTypeId">
              <el-select
                v-model="formData.certTypeId"
                placeholder="请选择证件类型"
                style="width: 100%"
                filterable
              >
                <el-option
                  v-for="certType in certTypeList"
                  :key="certType.id"
                  :label="`${certType.zjlbmc} (${certType.zjlbdm})`"
                  :value="certType.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="发行年份" prop="issueYear">
              <el-date-picker
                v-model="formData.issueYear"
                type="year"
                placeholder="请选择发行年份"
                style="width: 100%"
                value-format="YYYY"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="版本ID" prop="versionId">
              <el-input v-model="formData.versionId" placeholder="关联的版本ID" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 统计信息 -->
      <el-card class="mb-4" shadow="never">
        <template #header>
          <span class="card-title">统计信息</span>
        </template>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="文件总数">
              <el-input-number
                v-model="formData.fileCount"
                :min="0"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="已处理文件数">
              <el-input-number
                v-model="formData.processedFileCount"
                :min="0"
                :max="formData.fileCount"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="上传进度">
              <el-progress
                :percentage="uploadProgress"
                :stroke-width="20"
                text-inside
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 审核信息 -->
      <el-card v-if="formData.reviewStatus !== 'pending'" shadow="never">
        <template #header>
          <span class="card-title">审核信息</span>
        </template>

        <el-form-item label="审核意见">
          <el-input
            v-model="formData.reviewComments"
            type="textarea"
            :rows="3"
            placeholder="请输入审核意见"
          />
        </el-form-item>
      </el-card>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// import type { FormInstance, FormRules } from 'element-plus'
import { listCountry } from '@/api/cert/country'
import { listCertType } from '@/api/cert/certType'
import { updateFolderInfo } from '@/api/cert/folder'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  folderInfo: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible', 'refresh'])

// Refs
const formRef = ref()
const loading = ref(false)
const saving = ref(false)
const countryList = ref([])
const certTypeList = ref([])

// Form data
const formData = ref({
  folderId: '',
  folderName: '',
  folderType: 'regular',
  status: 'unassociated',
  reviewStatus: 'pending',
  versionAssociationMethod: 'manual_select',
  countryId: null,
  certTypeId: null,
  issueYear: '',
  versionId: '',
  fileCount: 0,
  processedFileCount: 0,
  reviewComments: ''
})

// Form rules
const formRules = {
  folderName: [
    { required: true, message: '请输入文件夹名称', trigger: 'blur' }
  ],
  folderType: [
    { required: true, message: '请选择样本类型', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ],
  countryId: [
    { required: true, message: '请选择国家', trigger: 'change' }
  ],
  certTypeId: [
    { required: true, message: '请选择证件类型', trigger: 'change' }
  ]
}

// Computed
const uploadProgress = computed(() => {
  if (formData.value.fileCount === 0) return 0
  return Math.round((formData.value.processedFileCount / formData.value.fileCount) * 100)
})

// Watch
watch(() => props.visible, (newVal) => {
  if (newVal && props.folderInfo) {
    loadFolderData()
    loadCountryList()
    loadCertTypeList()
  }
})

// Methods
const loadFolderData = () => {
  const folder = props.folderInfo
  formData.value = {
    folderId: folder.folderId || '',
    folderName: folder.folderName || folder.filename || '',
    folderType: folder.folderType || 'regular',
    status: folder.status || 'unassociated',
    reviewStatus: folder.reviewStatus || 'pending',
    versionAssociationMethod: folder.versionAssociationMethod || 'manual_select',
    countryId: folder.countryInfo?.id || null,
    certTypeId: folder.certInfo?.id || null,
    issueYear: folder.issueYear || '',
    versionId: folder.versionId || '',
    fileCount: folder.fileCount || 0,
    processedFileCount: folder.processedFileCount || 0,
    reviewComments: folder.reviewInfo?.reviewComments || ''
  }
}

const loadCountryList = async () => {
  try {
    const response = await listCountry()
    if (response.code === 200) {
      countryList.value = response.rows || response.data || []
    }
  } catch (error) {
    console.error('加载国家列表失败:', error)
  }
}

const loadCertTypeList = async () => {
  try {
    const response = await listCertType()
    if (response.code === 200) {
      certTypeList.value = response.rows || response.data || []
    }
  } catch (error) {
    console.error('加载证件类型列表失败:', error)
  }
}

const handleCountryChange = () => {
  // 国家变更时清空证件类型选择
  formData.value.certTypeId = null
}

const handleSave = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    await ElMessageBox.confirm(
      '确定要保存样本信息的修改吗？',
      '确认保存',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    saving.value = true

    const updateData = {
      ...formData.value,
      // 构建国家信息对象
      countryInfo: countryList.value.find(c => c.id === formData.value.countryId),
      // 构建证件类型信息对象
      certInfo: certTypeList.value.find(c => c.id === formData.value.certTypeId)
    }

    const response = await updateFolderInfo(formData.value.folderId, updateData)

    if (response.code === 200) {
      ElMessage.success('保存样本信息成功')
      emit('refresh')
      handleClose()
    } else {
      ElMessage.error(response.msg || '保存失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('保存样本信息失败:', error)
      ElMessage.error('保存失败')
    }
  } finally {
    saving.value = false
  }
}

const handleVisibleChange = (value) => {
  emit('update:visible', value)
}

const handleClose = () => {
  emit('update:visible', false)
  // 重置表单
  nextTick(() => {
    formRef.value?.resetFields()
  })
}
</script>

<style scoped>
.card-title {
  font-weight: 600;
  color: #303133;
}

.mb-4 {
  margin-bottom: 16px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-card__header) {
  padding: 12px 20px;
  background-color: #f5f7fa;
}

:deep(.el-card__body) {
  padding: 20px;
}
</style>
