<template>
  <div class="task-workspace">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="page-title">
        <h2>任务工作台</h2>
        <p>管理任务 {{ taskId }} 的文件夹资源与版本成果</p>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <el-container class="main-container">
      <!-- 左侧栏：待处理文件夹 -->
      <el-aside width="400px" class="left-panel">
        <div class="panel-header">
          <h3>待处理文件夹</h3>
          <el-tag type="info">{{ selectedFolderIds.length }} 个已选择</el-tag>
        </div>

        <UnassignedFolderList
          :task-id="taskId"
          @selection-change="handleFolderSelectionChange"
          @folder-selected="handleFolderSelected"
          @compare-clicked="handleCompareClicked"
          ref="unassignedFolderListRef"
        />
      </el-aside>

      <!-- 右侧栏：版本列表 -->
      <el-main class="right-panel">
        <div class="panel-header">
          <h3>版本成果</h3>
        </div>

        <VersionBrowser
          :context-keywords="contextKeywords"
          :can-create-version="!!selectedFolder"
          @create-version-clicked="handleCreateVersionClicked"
          @versions-selected="handleVersionsSelected"
          ref="versionBrowserRef"
        />
      </el-main>
    </el-container>

    <!-- 比对弹窗 -->
    <el-dialog
      v-model="showCompareDialog"
      title="图片比对"
      width="1200px"
      :close-on-click-modal="false"
      @close="handleCloseCompare"
    >
      <CompareArea
        :selected-folder="compareFolder"
        :selected-versions="selectedVersions"
        :current-version-index="currentVersionIndex"
        @version-change="handleVersionChange"
        @associate-folder="handleAssociateFolder"
        @close-compare="handleCloseCompare"
      />
    </el-dialog>

    <!-- 简单版本创建表单 -->
    <SimpleVersionForm
      v-model:visible="showVersionForm"
      :folder-info="selectedFolderForVersion"
      @success="handleVersionCreated"
      @close="handleVersionFormClose"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import UnassignedFolderList from '../folder/components/UnassignedFolderList.vue'
import VersionBrowser from '../version/components/VersionBrowser.vue'
import CompareArea from '@/views/cert/components/common/CompareArea.vue'
import SimpleVersionForm from '@/views/cert/components/common/SimpleVersionForm.vue'
import type { FolderInfoVO } from '@/api/cert/folder'
import type { CertVersionVO } from '@/api/cert/version'

// 路由参数
const route = useRoute()
const taskId = computed(() => route.params.taskId as string)

// 子组件引用
const unassignedFolderListRef = ref()
const versionBrowserRef = ref()

// 响应式数据
const selectedFolders = ref<FolderInfoVO[]>([])
const selectedFolderIds = ref<string[]>([])
const selectedFolder = ref<FolderInfoVO | null>(null) // 单选的文件夹
const selectedVersions = ref<CertVersionVO[]>([]) // 多选的版本
const currentVersionIndex = ref(0) // 当前显示的版本索引


// 添加新的响应式数据
const showCompareDialog = ref(false)
const compareFolder = ref<FolderInfoVO | null>(null)

// 版本创建相关状态
const showVersionForm = ref(false)
const selectedFolderForVersion = ref<FolderInfoVO | null>(null)



// 计算属性：从选中的文件夹中提取关键词
const contextKeywords = computed(() => {
  const keywords: string[] = []

  // 如果有单选的文件夹，使用单选文件夹的关键词
  if (selectedFolder.value?.folderName) {
    const parts = selectedFolder.value.folderName.split('_')
    if (parts.length > 0) {
      keywords.push(parts[0]) // 提取国家名
    }
    if (parts.length > 1) {
      keywords.push(parts[1]) // 提取证件类型
    }
  }

  // 兼容多选模式
  selectedFolders.value.forEach((folder: any) => {
    if (folder.folderName) {
      const parts = folder.folderName.split('_')
      if (parts.length > 0) {
        keywords.push(parts[0])
      }
      if (parts.length > 1) {
        keywords.push(parts[1])
      }
    }
  })

  // 去重并返回
  return [...new Set(keywords)]
})

// 计算属性：是否显示比对区域
const showCompareArea = computed(() => {
  console.log('显示比对区域:', selectedFolder.value, selectedVersions.value.length)
  return selectedFolder.value && selectedVersions.value.length > 0
})

// 文件夹名称缓存（用于显示）
const folderNameCache = ref<Map<string, string>>(new Map())

// 获取文件夹名称
const getFolderNameById = (folderId: string): string => {
  return folderNameCache.value.get(folderId) || folderId
}

// 处理文件夹选择变化
const handleFolderSelectionChange = (selection: { folderIds: string[], folders: FolderInfoVO[] }) => {
  selectedFolderIds.value = selection.folderIds
  selectedFolders.value = selection.folders

  // 更新文件夹名称缓存
  selection.folders.forEach(folder => {
    folderNameCache.value.set(folder.folderId, folder.folderName)
  })
}

// 处理文件夹单选
const handleFolderSelected = (folder: FolderInfoVO | null) => {
  console.log('文件夹选择变化:', folder)
  selectedFolder.value = folder
  // 重置版本索引
  currentVersionIndex.value = 0
}

// 处理版本多选
const handleVersionsSelected = (versions: CertVersionVO[]) => {
  console.log('版本选择变化:', versions)
  selectedVersions.value = versions
  // 重置版本索引
  currentVersionIndex.value = 0
}

// 处理版本切换
const handleVersionChange = (index: number) => {
  currentVersionIndex.value = index
}

// 处理关联文件夹
const handleAssociateFolder = async (folder: FolderInfoVO, version: CertVersionVO) => {
  try {
    console.log('关联文件夹到版本:', folder, version)
    ElMessage.success(`成功关联文件夹 "${folder.folderName}" 到版本 "${version.versionCode}"`)

    // 关联成功后关闭弹窗
    showCompareDialog.value = false
    compareFolder.value = null

    // 刷新数据
    await refreshComponents()
  } catch (error) {
    console.error('关联失败:', error)
    ElMessage.error('关联失败，请重试')
  }
}

// 处理关闭比对
const handleCloseCompare = () => {
  showCompareDialog.value = false
  compareFolder.value = null
  // 不清空版本选择，保持右侧的选择状态
}

// 处理创建版本按钮点击
const handleCreateVersionClicked = () => {
  // 检查是否有选中的文件夹（单选模式）
  if (!selectedFolder.value) {
    ElMessage.warning('请先选择一个待处理的文件夹')
    return
  }

  // 设置选中的文件夹并打开版本创建表单
  selectedFolderForVersion.value = selectedFolder.value
  showVersionForm.value = true
}

// 版本创建成功处理
const handleVersionCreated = async () => {
  ElMessage.success('版本创建成功')

  // 清空选择
  selectedFolder.value = null
  selectedFolderForVersion.value = null

  // 刷新数据
  await refreshComponents()
}

// 版本创建表单关闭处理
const handleVersionFormClose = () => {
  selectedFolderForVersion.value = null
}



// 处理比对按钮点击
const handleCompareClicked = (folder: FolderInfoVO) => {
  console.log('打开比对弹窗:', folder)
  compareFolder.value = folder
  showCompareDialog.value = true
}

// 刷新子组件数据
const refreshComponents = async () => {
  try {
    // 刷新未分配文件夹列表
    if (unassignedFolderListRef.value?.refreshData) {
      await unassignedFolderListRef.value.refreshData()
    }

    // 刷新版本浏览器
    if (versionBrowserRef.value?.refreshData) {
      await versionBrowserRef.value.refreshData()
    }
  } catch (error) {
    console.error('刷新组件数据失败:', error)
  }
}

// 生命周期
onMounted(() => {
  if (!taskId.value) {
    ElMessage.error('任务ID不能为空')
    return
  }
})
</script>

<style scoped>
.task-workspace {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

.page-header {
  padding: 20px 24px 0;
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
}

.page-title h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-title p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.main-container {
  flex: 1;
  height: auto;
}

.left-panel {
  background-color: #fff;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
  padding: 0;
}

.right-panel {
  background-color: #fff;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fafafa;
}

.panel-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.create-version-content {
  padding: 20px 0;
}

.selected-folders-info {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.selected-folders-info h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.folder-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.folder-tag {
  margin: 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-aside) {
  overflow: hidden;
  padding: 0 !important;
}

:deep(.el-main) {
  padding: 0;
  overflow: hidden;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}


/* 调整弹窗内容的样式 */
:deep(.el-dialog__body) {
  padding: 0;
}

.selected-folder-info {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.selected-folder-info h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.folder-info {
  display: flex;
  justify-content: center;
}
</style>
