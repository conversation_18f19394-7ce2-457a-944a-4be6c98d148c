package com.ruoyi.system.domain.dto.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 版本关联请求DTO
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
public class VersionAssociationDTO {

    /** 版本ID */
    @NotBlank(message = "版本ID不能为空")
    private String versionId;

    // 手动添加getter/setter方法以确保编译通过
    public String getVersionId() {
        return versionId;
    }

    public void setVersionId(String versionId) {
        this.versionId = versionId;
    }
}
