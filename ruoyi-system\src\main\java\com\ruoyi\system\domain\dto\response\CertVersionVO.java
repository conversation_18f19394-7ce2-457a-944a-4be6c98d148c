package com.ruoyi.system.domain.dto.response;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.system.domain.Country;
import com.ruoyi.system.domain.CertType;
import com.ruoyi.system.domain.mongo.TrainingImage;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 证件版本响应VO
 * 用于展示证件版本的详情
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
public class CertVersionVO {
    
    /** MongoDB主键 */
    private String id;
    
    /** 业务版本ID */
    private String versionId;
    
    /** 版本代码 */
    private String versionCode;
    
    /** 描述 */
    private String description;
    
    /** 国家信息 */
    private Country countryInfo;
    
    /** 证件类型信息 */
    private CertType certInfo;
    
    /** 发行年份 */
    private String issueYear;
    
    /** 状态 */
    private String status;
    
    /** 关联的标准文件夹ID */
    private String standardFolderId;
    
    /** 训练图片信息 */
    private TrainingImage trainingImage;
    
    /** 部门信息 */
    private SysDept deptInfo;
    
    /** 创建者信息 */
    private SysUser creatorInfo;
    
    /** 创建时间 */
    private LocalDateTime createTime;
    
    /** 更新时间 */
    private LocalDateTime updateTime;
}
