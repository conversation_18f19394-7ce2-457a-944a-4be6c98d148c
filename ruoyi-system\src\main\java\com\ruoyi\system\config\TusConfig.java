package com.ruoyi.system.config;

import com.ruoyi.system.domain.mongo.BatchUploadTask;
import com.ruoyi.system.repository.BatchUploadTaskRepository;
import com.ruoyi.system.service.IBatchTaskService;
import me.desair.tus.server.TusFileUploadService;
import me.desair.tus.server.upload.UploadInfo;
import org.springframework.util.StringUtils;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.BucketExistsArgs;
import io.minio.MakeBucketArgs;
import com.ruoyi.common.config.MinioConfig;
import javax.annotation.PostConstruct;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Tus文件上传服务配置
 * 
 * 配置Tus协议的断点续传文件上传服务，集成MinIO存储和批量任务处理。
 * 
 * <AUTHOR>
 * @date 2025-07-04
 * @modified 2025-07-04 - 修复offset值错误问题，增强错误处理
 */
@Configuration
public class TusConfig {

    private static final Logger log = LoggerFactory.getLogger(TusConfig.class);

    @Value("${tus.upload.storage-path:/tmp/tus-uploads}")
    private String tusStoragePath;

    @Autowired
    private MinioClient minioClient;

    @Autowired
    private MinioConfig minioConfig;

    @Autowired
    private IBatchTaskService batchTaskService;

    @Autowired
    private BatchUploadTaskRepository batchUploadTaskRepository;

    @Value("${minio.bucketName}")
    private String bucketName;

    @Value("${tus.upload.expiration-period:86400000}")
    private Long expirationPeriod;

    @Value("${tus.upload.max-upload-size:1073741824}")
    private Long maxUploadSize;

    @Value("${tus.upload.download-enabled:true}")
    private Boolean downloadEnabled;

    /**
     * 初始化MinIO连接测试
     */
    @PostConstruct
    public void initMinioConnection() {
        try {
            log.info("开始测试MinIO连接...");

            // 测试存储桶是否存在
            boolean bucketExists = minioClient.bucketExists(
                BucketExistsArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .build()
            );

            if (!bucketExists) {
                log.info("存储桶 {} 不存在，正在创建...", minioConfig.getBucketName());
                minioClient.makeBucket(
                    MakeBucketArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .build()
                );
                log.info("存储桶 {} 创建成功", minioConfig.getBucketName());
            } else {
                log.info("存储桶 {} 已存在", minioConfig.getBucketName());
            }

            log.info("MinIO连接测试成功，URL: {}, Bucket: {}", minioConfig.getUrl(), minioConfig.getBucketName());

        } catch (Exception e) {
            log.error("MinIO连接测试失败: {}", e.getMessage(), e);
            log.warn("MinIO服务可能未启动或配置错误，文件上传功能可能受影响");
        }
    }

    /**
     * 创建TusFileUploadService Bean
     *
     * 配置Tus服务使用磁盘存储，修复offset值错误问题。
     * 注意：tus-java-server 没有内建的上传完成回调机制，
     * 需要在控制器中通过检测 UploadInfo.isUploadInProgress() 来判断上传是否完成。
     *
     * @return 配置好的TusFileUploadService实例
     */
    @Bean
    public TusFileUploadService tusFileUploadService() {
        log.info("初始化TusFileUploadService，使用磁盘存储");
        log.info("配置参数 - 存储路径: {}, 过期时间: {}ms, 最大文件大小: {}字节",
                tusStoragePath, expirationPeriod, maxUploadSize);

        try {
            // 确保存储目录存在
            java.io.File storageDir = new java.io.File(tusStoragePath);
            if (!storageDir.exists()) {
                boolean created = storageDir.mkdirs();
                log.info("创建TUS存储目录: {}, 结果: {}", tusStoragePath, created);
            }

            // 创建 TusFileUploadService，设置正确的 uploadURI
            TusFileUploadService service = new TusFileUploadService()
                    .withStoragePath(tusStoragePath) // 使用磁盘存储路径
                    .withUploadURI("/tus/uploads") // 设置上传端点，确保与前端配置一致
                    .withUploadExpirationPeriod(expirationPeriod)
                    .withMaxUploadSize(maxUploadSize);

            // 可选功能配置
            if (downloadEnabled) {
                service.withDownloadFeature();
                log.info("启用Tus下载功能");
            }

            log.info("TusFileUploadService初始化完成，uploadURI: /tus/uploads");
            return service;
            
        } catch (Exception e) {
            log.error("TusFileUploadService初始化失败", e);
            throw new RuntimeException("TUS服务初始化失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理文件上传完成的业务逻辑
     *
     * 这个方法将在文件上传完成后被控制器调用。
     * 使用正确的Tus上传完成检测方案，通过InputStream读取完整文件内容。
     * 支持多级文件夹路径映射和原始文件名保持。
     *
     * @param uploadInfo Tus上传信息对象，包含元数据
     * @param inputStream 上传文件的输入流
     */
    public void handleUploadCompletion(UploadInfo uploadInfo, java.io.InputStream inputStream) {
        try {
            log.info("处理文件上传完成，uploadId: {}", uploadInfo.getId().toString());

            // 1. 从metadata中解析业务参数
            Map<String, String> metadata = uploadInfo.getMetadata();
            String taskId = metadata.get("taskId");
            String certNumberPrefix = metadata.get("certNumberPrefix");
            String issuePlace = metadata.get("issuePlace");
            String originalFileName = metadata.get("filename");
            String folderName = metadata.get("folderName"); // 获取文件夹名称
            String folderPath = metadata.get("folderPath"); // 获取完整文件夹路径

            log.info("解析到的元数据: taskId={}, certNumberPrefix={}, issuePlace={}, originalFileName={}, folderName={}, folderPath={}",
                    taskId, certNumberPrefix, issuePlace, originalFileName, folderName, folderPath);

            // 验证必需参数
            if (!StringUtils.hasText(taskId)) {
                log.error("上传元数据中缺少taskId，无法处理上传完成事件，metadata: {}", metadata);
                return;
            }
            if (!StringUtils.hasText(originalFileName)) {
                log.error("上传元数据中缺少原始文件名，无法处理上传完成事件，metadata: {}", metadata);
                return;
            }
            if (!StringUtils.hasText(folderName)) {
                log.error("上传元数据中缺少文件夹名称，无法处理上传完成事件，metadata: {}", metadata);
                return;
            }

            // 2. 查询批量上传任务获取完整上下文
            Optional<BatchUploadTask> taskOpt = batchUploadTaskRepository.findByTaskId(taskId);
            if (!taskOpt.isPresent()) {
                log.error("无法找到taskId对应的批量上传任务: {}", taskId);
                return;
            }

            BatchUploadTask task = taskOpt.get();
            log.info("找到批量上传任务: {}, 任务状态: {}",
                    task.getTaskName(),
                    task.getStatus());

            // 3. 构建最终的MinIO存储路径（使用任务名称而不是taskId）
            String finalMinioPath = buildFinalMinioPath(task.getTaskName(), folderName, originalFileName);
            log.info("计算出最终MinIO路径: {}", finalMinioPath);

            // 4. 上传文件到MinIO
            log.info("开始上传文件到MinIO，文件大小: {} 字节", uploadInfo.getLength());
            String actualMinioPath = uploadFileToMinio(inputStream, finalMinioPath, originalFileName, uploadInfo.getLength());
            log.info("文件上传到MinIO成功，实际路径: {}", actualMinioPath);

            // 5. 调用业务服务更新数据库
            batchTaskService.handleUploadCompletion(
                    taskId,
                    issuePlace != null ? issuePlace : "未知", // 使用元数据中的issuePlace或默认值
                    certNumberPrefix != null ? certNumberPrefix : "NA",
                    originalFileName,
                    actualMinioPath,  // 使用实际的MinIO路径
                    folderName,       // 传递文件夹名称
                    folderPath        // 传递完整文件夹路径
            );

            log.info("文件上传完成处理成功，taskId: {}, 实际MinIO路径: {}", taskId, actualMinioPath);

        } catch (Exception e) {
            log.error("处理文件上传完成时发生异常，uploadId: {}", uploadInfo.getId().toString(), e);
            throw new RuntimeException("文件上传完成处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建文件在MinIO中的最终存储路径
     *
     * 路径格式：taskName/folderName/originalFileName
     * 保持原始文件名，支持文件夹结构映射
     *
     * @param taskName 任务名称（更具可读性）
     * @param folderName 文件夹名称
     * @param originalFileName 原始文件名
     * @return 最终的MinIO存储路径
     */
    private String buildFinalMinioPath(String taskName, String folderName, String originalFileName) {
        // 清理文件夹名称，移除特殊字符但保留中文
        String cleanFolderName = folderName;
        if (StringUtils.hasText(folderName)) {
            // 只替换可能导致路径问题的字符，保留中文和常用符号
            cleanFolderName = folderName.replaceAll("[\\\\/:*?\"<>|]", "_");
        }

        // 清理原始文件名
        String cleanFileName = originalFileName;
        if (StringUtils.hasText(originalFileName)) {
            // 只替换路径分隔符，保留其他字符
            cleanFileName = originalFileName.replaceAll("[\\\\/:*?\"<>|]", "_");
        }

        // 清理任务名称，移除特殊字符但保留中文
        String cleanTaskName = taskName;
        if (StringUtils.hasText(taskName)) {
            // 只替换可能导致路径问题的字符，保留中文和常用符号
            cleanTaskName = taskName.replaceAll("[\\\\/:*?\"<>|]", "_");
        }

        // 构建路径：taskName/folderName/originalFileName
        return String.format("%s/%s/%s", cleanTaskName, cleanFolderName, cleanFileName);
    }

    /**
     * 上传文件到MinIO
     *
     * @param inputStream 文件输入流
     * @param objectName MinIO对象名称（路径）
     * @param originalFileName 原始文件名
     * @param fileSize 文件大小
     * @return 实际的MinIO存储路径
     * @throws Exception 上传异常
     */
    private String uploadFileToMinio(java.io.InputStream inputStream, String objectName, String originalFileName, Long fileSize) throws Exception {
        try {
            log.info("开始上传文件到MinIO: objectName={}, originalFileName={}, fileSize={}", objectName, originalFileName, fileSize);

            // 获取文件扩展名来确定Content-Type
            String contentType = getContentType(originalFileName);

            // 上传文件到MinIO
            minioClient.putObject(
                PutObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(objectName)
                    .stream(inputStream, fileSize, -1)
                    .contentType(contentType)
                    .build()
            );

            log.info("文件上传到MinIO成功: bucket={}, object={}", minioConfig.getBucketName(), objectName);
            return objectName;

        } catch (Exception e) {
            log.error("上传文件到MinIO失败: objectName={}, error={}", objectName, e.getMessage(), e);
            throw new RuntimeException("MinIO文件上传失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据文件名获取Content-Type
     *
     * @param fileName 文件名
     * @return Content-Type
     */
    private String getContentType(String fileName) {
        if (fileName == null) {
            return "application/octet-stream";
        }

        String extension = fileName.toLowerCase();
        if (extension.endsWith(".jpg") || extension.endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (extension.endsWith(".png")) {
            return "image/png";
        } else if (extension.endsWith(".pdf")) {
            return "application/pdf";
        } else if (extension.endsWith(".gif")) {
            return "image/gif";
        } else if (extension.endsWith(".bmp")) {
            return "image/bmp";
        } else if (extension.endsWith(".tiff") || extension.endsWith(".tif")) {
            return "image/tiff";
        } else {
            return "application/octet-stream";
        }
    }
}
