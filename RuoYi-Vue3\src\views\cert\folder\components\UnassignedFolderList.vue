<template>
  <div class="unassigned-folder-list">
    <div v-loading="loading" class="folder-list">
      <FolderListItem
        v-for="folder in unassignedFolders"
        :key="folder.folderId"
        :folder-name="folder.folderName"
        :thumbnail-url="getMainPicUrl(folder.mainPicPath)"
        :show-checkbox="false"
        :show-radio="true"
        :is-selected="selectedFolderId === folder.folderId"
        :show-compare-button="true"
        @radio-change="handleFolderSelect(folder.folderId)"
        @folder-clicked="handleFolderSelect(folder.folderId)"
        @compare-clicked="handleCompareClicked(folder)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineExpose } from 'vue'
import { ElMessage } from 'element-plus'
import FolderListItem from './FolderListItem/'
import { getFolderList, type FolderInfoVO } from '@/api/cert/folder'

// 定义Props
interface Props {
  taskId: string
}

const props = defineProps<Props>()

// 定义Emits
const emit = defineEmits<{
  'selection-change': [selection: { folderIds: string[], folders: FolderInfoVO[] }]
  'folder-selected': [folder: FolderInfoVO | null]
  'compare-clicked': [folder: FolderInfoVO]
}>()

// 响应式数据
const loading = ref(false)
const unassignedFolders = ref<FolderInfoVO[]>([])
const selectedFolders = ref<string[]>([])
const selectedFolderId = ref<string>('') // 单选模式：当前选中的文件夹ID

// 添加比对事件
const handleCompareClicked = (folder: FolderInfoVO) => {
  console.log('点击比对按钮:', folder)
  emit('compare-clicked', folder)
}

// 获取未分配的文件夹数据
const loadUnassignedFolders = async () => {
  loading.value = true
  try {
    const response = await getFolderList({
      status: 'unassociated',
      taskId: props.taskId
    })
    if (response.code === 200) {
      unassignedFolders.value = response.rows || []
    } else {
      ElMessage.error(response.msg || '加载未分配文件夹失败')
    }
  } catch (error) {
    console.error('加载未分配文件夹失败:', error)
    ElMessage.error('加载未分配文件夹失败')
  } finally {
    loading.value = false
  }
}

// 处理文件夹选择（保留多选功能用于兼容）
const handleSelection = (folderId: string) => {
  const index = selectedFolders.value.indexOf(folderId)
  if (index > -1) {
    selectedFolders.value.splice(index, 1)
  } else {
    selectedFolders.value.push(folderId)
  }

  // 触发选择变化事件
  const selectedFolderObjects = unassignedFolders.value.filter(folder =>
    selectedFolders.value.includes(folder.folderId)
  )

  emit('selection-change', {
    folderIds: selectedFolders.value,
    folders: selectedFolderObjects
  })
}

// 处理文件夹单选
const handleFolderSelect = (folderId: string) => {
  console.log('选择文件夹:', folderId)

  // 如果点击的是已选中的文件夹，则取消选择
  if (selectedFolderId.value === folderId) {
    selectedFolderId.value = ''
    emit('folder-selected', null)
  } else {
    selectedFolderId.value = folderId
    const selectedFolder = unassignedFolders.value.find(folder => folder.folderId === folderId)
    emit('folder-selected', selectedFolder || null)
  }
}

// 构建主图URL
const getMainPicUrl = (mainPicPath: string | undefined) => {
  if (!mainPicPath) {
    console.log('mainPicPath 为空，返回空字符串')
    return ''
  }

  console.log('构建主图URL，原始路径:', mainPicPath)

  // 如果已经是完整URL，直接返回
  if (mainPicPath.startsWith('http://') || mainPicPath.startsWith('https://')) {
    console.log('已是完整URL，直接返回:', mainPicPath)
    return mainPicPath
  }

  // 构建MinIO访问URL
  const directMinioUrl = `http://localhost:9000/xjlfiles/${mainPicPath}`
  const proxyUrl = `/dev-api/common/image/proxy?url=${encodeURIComponent(directMinioUrl)}`

  console.log('生成的代理URL:', proxyUrl)
  return proxyUrl
}

// 刷新数据方法（供父组件调用）
const refreshData = async () => {
  await loadUnassignedFolders()
  // 清空选择
  selectedFolders.value = []
  selectedFolderId.value = ''
  emit('selection-change', { folderIds: [], folders: [] })
  emit('folder-selected', null)
}

// 暴露方法给父组件
defineExpose({
  refreshData
})

// 生命周期
onMounted(() => {
  if (props.taskId) {
    loadUnassignedFolders()
  }
})
</script>

<style scoped>
.unassigned-folder-list {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  height: 100%;
}

.folder-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
}
</style>
