package com.ruoyi.system.service.impl;

import com.ruoyi.system.domain.Country;
import com.ruoyi.system.domain.CertType;
import com.ruoyi.system.domain.mongo.FolderInfo;
import com.ruoyi.system.domain.mongo.FolderInfo.PreParseVersionInfo;
import com.ruoyi.system.domain.dto.response.FolderInfoVO;
import com.ruoyi.system.service.ICountryService;
import com.ruoyi.system.service.ICertTypeService;
import com.ruoyi.system.service.IFolderInfoService;
import com.ruoyi.system.service.IVersionNameParseService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.scheduling.annotation.Async;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 版本名称解析服务实现
 * 
 * <AUTHOR>
 * @date 2025-07-06
 */
@Slf4j
@Service
public class VersionNameParseServiceImpl implements IVersionNameParseService {

    @Autowired
    private ICountryService countryService;

    @Autowired
    private ICertTypeService certTypeService;

    @Autowired
    private IFolderInfoService folderInfoService;
    

    
    @Override
    public PreParseVersionInfo parseFolderNameToVersionInfo(String folderName) {
        log.info("开始解析文件夹名称: {}", folderName);
        
        PreParseVersionInfo parseInfo = new PreParseVersionInfo();
        parseInfo.setOriginalFolderName(folderName);
        parseInfo.setParseTime(new Date());
        parseInfo.setParseErrors(new ArrayList<>());
        
        try {
            // 按下划线分割文件夹名称
            String[] parts = folderName.split("_");
            
            if (parts.length < 5) {
                parseInfo.setParseStatus("FAILED");
                parseInfo.getParseErrors().add("文件夹名称格式不正确，应为：国家_证件类型_年份_证件号前缀_签发地");
                return parseInfo;
            }
            
            String countryName = parts[0];
            String certTypeName = parts[1];
            String issueYear = parts[2];
            String certNumberPrefix = parts[3];

            String issuePlace = parts[4];
            
            // 解析国家信息
            String countryCode = findCountryCodeByNameCn(countryName);
            if (countryCode == null) {
                parseInfo.getParseErrors().add("未找到匹配的国家代码: " + countryName);
                countryCode = countryName.toUpperCase(); // 使用原名称作为备选
            }
            
            // 解析证件类型信息
            String certTypeCode = findCertTypeCodeByName(certTypeName);
            if (certTypeCode == null) {
                parseInfo.getParseErrors().add("未找到匹配的证件类型代码: " + certTypeName);
                certTypeCode = "UNKNOWN"; // 使用默认值
            }
            
            // 直接使用原始签发地名称，不进行翻译
            String englishPlace = issuePlace;
            
            // 生成版本代码 20250710 暂时不使用签发地，不容易标准化
//            String versionCode = String.format("%s_%s_%s_%s_%s",
//                countryCode, certTypeCode, issueYear, certNumberPrefix, englishPlace);
            String versionCode = String.format("%s_%s_%s_%s",
                    countryCode, certTypeCode, issueYear, certNumberPrefix);
            // 设置解析结果
            parseInfo.setCountryName(countryName);
            parseInfo.setCountryCode(countryCode);
            parseInfo.setCertTypeName(certTypeName);
            parseInfo.setCertTypeCode(certTypeCode);
            parseInfo.setIssueYear(issueYear);
            parseInfo.setCertNumberPrefix(certNumberPrefix);
            parseInfo.setIssuePlace(englishPlace);
            parseInfo.setParsedVersionCode(versionCode);
            
            // 设置解析状态
            if (parseInfo.getParseErrors().isEmpty()) {
                parseInfo.setParseStatus("SUCCESS");
            } else {
                parseInfo.setParseStatus("PARTIAL");
            }
            
            log.info("解析完成，版本代码: {}", versionCode);
            return parseInfo;
            
        } catch (Exception e) {
            log.error("解析文件夹名称失败: {}", e.getMessage(), e);
            parseInfo.setParseStatus("FAILED");
            parseInfo.getParseErrors().add("解析过程中发生异常: " + e.getMessage());
            return parseInfo;
        }
    }
    
    @Override
    public String findCountryCodeByNameCn(String countryNameCn) {
        try {
            if (!StringUtils.hasText(countryNameCn)) {
                return null;
            }

            log.debug("查询国家代码，中文名称: {}", countryNameCn);

            // 使用模糊查询查找国家 (MySQL Country表name_cn字段模糊匹配)
            List<Country> countries = countryService.searchCountries(countryNameCn);
            if (countries != null && !countries.isEmpty()) {
                // 优先精确匹配
                for (Country country : countries) {
                    if (countryNameCn.equals(country.getName())) {
                        log.debug("精确匹配国家: {} -> {}", countryNameCn, country.getCode());
                        return country.getCode();
                    }
                }

                // 如果没有精确匹配，返回第一个模糊匹配结果
                Country firstMatch = countries.get(0);
                log.debug("模糊匹配国家: {} -> {} ({})", countryNameCn, firstMatch.getCode(), firstMatch.getName());
                return firstMatch.getCode();
            }

            log.warn("未找到匹配的国家: {}", countryNameCn);
            return null;
        } catch (Exception e) {
            log.error("查询国家代码失败，输入: {}, 错误: {}", countryNameCn, e.getMessage(), e);
            return null;
        }
    }
    
    @Override
    public String findCertTypeCodeByName(String certTypeName) {
        try {
            if (!StringUtils.hasText(certTypeName)) {
                return null;
            }

            log.debug("查询证件类型代码，名称: {}", certTypeName);

            // 创建查询条件 (MySQL CertType表zjlbmc字段模糊匹配)
            CertType queryCondition = new CertType();
            queryCondition.setZjlbmc(certTypeName);

            // 查询证件类型列表
            List<CertType> certTypes = certTypeService.selectCertTypeList(queryCondition);
            if (certTypes != null && !certTypes.isEmpty()) {
                // 优先精确匹配
                for (CertType certType : certTypes) {
                    if (certTypeName.equals(certType.getZjlbmc())) {
                        String zyType = certType.getZyType();
                        if (StringUtils.hasText(zyType)) {
                            log.debug("精确匹配证件类型: {} -> {}", certTypeName, zyType);
                            return zyType;
                        }
                    }
                }

                // 如果没有精确匹配，返回第一个模糊匹配结果的zy_type
                CertType firstMatch = certTypes.get(0);
                String zyType = firstMatch.getZyType();
                if (StringUtils.hasText(zyType)) {
                    log.debug("模糊匹配证件类型: {} -> {} ({})", certTypeName, zyType, firstMatch.getZjlbmc());
                    return zyType;
                } else {
                    // 如果zy_type为空，使用zjlbdm
                    log.debug("zy_type为空，使用zjlbdm: {} -> {} ({})", certTypeName, firstMatch.getZjlbdm(), firstMatch.getZjlbmc());
                    return firstMatch.getZjlbdm();
                }
            }

            log.warn("未找到匹配的证件类型: {}", certTypeName);
            return null;
        } catch (Exception e) {
            log.error("查询证件类型代码失败，输入: {}, 错误: {}", certTypeName, e.getMessage(), e);
            return null;
        }
    }
    
    @Override
    public String translatePlaceNameToEnglish(String chinesePlaceName) {
        // 直接返回原始地名，不进行翻译
        return chinesePlaceName;
    }
    
    @Override
    public String generateTaskName(String deptName) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd_HHmmss");
            String timestamp = sdf.format(new Date());

            // 格式: yyyyMMdd_HHmmss_部门名称_批量上传任务
            String taskName = String.format("%s_%s_批量上传任务", timestamp,
                StringUtils.hasText(deptName) ? deptName : "未知部门");

            log.debug("生成任务名称: {}", taskName);
            return taskName;
        } catch (Exception e) {
            log.error("生成任务名称失败: {}", e.getMessage(), e);
            // 返回默认格式
            return "批量上传任务_" + System.currentTimeMillis();
        }
    }

    @Override
    @Async
    public void parseAllFoldersAsync(String taskId) {
        log.info("开始异步解析任务下的所有文件夹, taskId: {}", taskId);

        try {
            if (!StringUtils.hasText(taskId)) {
                log.error("任务ID不能为空");
                return;
            }

            // 1. 根据taskId查询所有文件夹
            List<FolderInfoVO> folders = folderInfoService.getFoldersByTaskId(taskId);
            if (folders == null || folders.isEmpty()) {
                log.warn("任务[{}]下没有找到文件夹", taskId);
                return;
            }

            log.info("任务[{}]下共找到{}个文件夹，开始解析", taskId, folders.size());

            int successCount = 0;
            int partialCount = 0;
            int failedCount = 0;

            // 2. 遍历文件夹，调用parseFolderNameToVersionInfo解析
            for (FolderInfoVO folderVO : folders) {
                try {
                    // 获取原始文件夹名称
                    String folderName = folderVO.getFolderName();
                    if (!StringUtils.hasText(folderName)) {
                        log.warn("文件夹[{}]名称为空，跳过解析", folderVO.getFolderId());
                        failedCount++;
                        continue;
                    }

                    log.debug("解析文件夹: {} ({})", folderVO.getFolderId(), folderName);

                    // 解析文件夹名称
                    PreParseVersionInfo parseInfo = parseFolderNameToVersionInfo(folderName);

                    // 3. 更新MongoDB中的FolderInfo状态
                    FolderInfo folderInfo = folderInfoService.getFolderByMongoId(folderVO.getId());
                    if (folderInfo != null) {
                        folderInfo.setFilename(folderName); // 设置原始文件夹名称
                        folderInfo.setPreParseVersionInfo(parseInfo); // 设置解析结果
                        folderInfo.setUpdateTime(new Date());

                        folderInfoService.updateFolder(folderInfo);

                        // 统计解析结果
                        switch (parseInfo.getParseStatus()) {
                            case "SUCCESS":
                                successCount++;
                                break;
                            case "PARTIAL":
                                partialCount++;
                                break;
                            case "FAILED":
                            default:
                                failedCount++;
                                break;
                        }

                        log.debug("文件夹[{}]解析完成，状态: {}", folderVO.getFolderId(), parseInfo.getParseStatus());
                    } else {
                        log.error("无法获取文件夹[{}]的MongoDB实体", folderVO.getFolderId());
                        failedCount++;
                    }

                } catch (Exception e) {
                    log.error("解析文件夹[{}]失败: {}", folderVO.getFolderId(), e.getMessage(), e);
                    failedCount++;
                }
            }

            log.info("任务[{}]异步解析完成，成功: {}, 部分成功: {}, 失败: {}",
                taskId, successCount, partialCount, failedCount);

        } catch (Exception e) {
            log.error("异步解析任务[{}]失败: {}", taskId, e.getMessage(), e);
        }
    }

    @Override
    public AjaxResult reparseFolder(String folderId) {
        log.info("解析文件夹版本信息, folderId: {}", folderId);

        try {
            if (!StringUtils.hasText(folderId)) {
                return AjaxResult.error("文件夹ID不能为空");
            }

            // 1. 根据folderId查询文件夹信息
            FolderInfo folderInfo = folderInfoService.getFolderByMongoId(folderId);
            if (folderInfo == null) {
                return AjaxResult.error("文件夹不存在: " + folderId);
            }

            String folderName = folderInfo.getFolderName();
            if (!StringUtils.hasText(folderName)) {
                return AjaxResult.error("文件夹名称为空，无法解析");
            }

            log.debug("重新解析文件夹: {} ({})", folderId, folderName);

            // 2. 重新解析文件夹名称
            PreParseVersionInfo parseInfo = parseFolderNameToVersionInfo(folderName);

            // 3. 更新解析结果
            folderInfo.setFilename(folderName);
            folderInfo.setPreParseVersionInfo(parseInfo);
            folderInfo.setUpdateTime(new Date());

            folderInfoService.updateFolder(folderInfo);

            log.info("文件夹[{}]重新解析完成，状态: {}", folderId, parseInfo.getParseStatus());

            Map<String, Object> result = new HashMap<>();
            result.put("folderId", folderId);
            result.put("parseStatus", parseInfo.getParseStatus());
            result.put("parsedVersionCode", parseInfo.getParsedVersionCode());
            result.put("parseErrors", parseInfo.getParseErrors());

            return AjaxResult.success("重新解析成功", result);

        } catch (Exception e) {
            log.error("重新解析文件夹失败, folderId: {}", folderId, e);
            return AjaxResult.error("重新解析失败: " + e.getMessage());
        }
    }

    @Override
    public AjaxResult reparseAllFolders(String taskId) {
        log.info("批量重新解析任务下的所有文件夹, taskId: {}", taskId);

        try {
            if (!StringUtils.hasText(taskId)) {
                return AjaxResult.error("任务ID不能为空");
            }

            // 1. 根据taskId查询所有文件夹
            List<FolderInfoVO> folders = folderInfoService.getFoldersByTaskId(taskId);
            if (folders == null || folders.isEmpty()) {
                return AjaxResult.error("任务下没有找到文件夹: " + taskId);
            }

            log.info("任务[{}]下共找到{}个文件夹，开始批量重新解析", taskId, folders.size());

            int successCount = 0;
            int partialCount = 0;
            int failedCount = 0;
            List<String> errorMessages = new ArrayList<>();

            // 2. 批量重新解析
            for (FolderInfoVO folderVO : folders) {
                try {
                    AjaxResult result = reparseFolder(folderVO.getId());
                    if (result.isSuccess()) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> data = (Map<String, Object>) result.get("data");
                        String parseStatus = (String) data.get("parseStatus");

                        switch (parseStatus) {
                            case "SUCCESS":
                                successCount++;
                                break;
                            case "PARTIAL":
                                partialCount++;
                                break;
                            case "FAILED":
                            default:
                                failedCount++;
                                break;
                        }
                    } else {
                        failedCount++;
                        errorMessages.add("文件夹[" + folderVO.getFolderId() + "]: " + result.get(AjaxResult.MSG_TAG));
                    }
                } catch (Exception e) {
                    failedCount++;
                    errorMessages.add("文件夹[" + folderVO.getFolderId() + "]: " + e.getMessage());
                    log.error("重新解析文件夹[{}]失败: {}", folderVO.getFolderId(), e.getMessage(), e);
                }
            }

            // 3. 返回解析结果统计
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("taskId", taskId);
            statistics.put("totalFolders", folders.size());
            statistics.put("successCount", successCount);
            statistics.put("partialCount", partialCount);
            statistics.put("failedCount", failedCount);
            statistics.put("errorMessages", errorMessages);

            log.info("任务[{}]批量重新解析完成，成功: {}, 部分成功: {}, 失败: {}",
                taskId, successCount, partialCount, failedCount);

            return AjaxResult.success("批量重新解析完成", statistics);

        } catch (Exception e) {
            log.error("批量重新解析失败, taskId: {}", taskId, e);
            return AjaxResult.error("批量重新解析失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getParseStatistics(String taskId) {
        log.info("获取解析状态统计, taskId: {}", taskId);
        Map<String, Object> statistics = new HashMap<>();

        try {
            if (!StringUtils.hasText(taskId)) {
                statistics.put("error", "任务ID不能为空");
                return statistics;
            }

            // 1. 查询任务下所有文件夹的解析状态
            List<FolderInfoVO> folders = folderInfoService.getFoldersByTaskId(taskId);
            if (folders == null || folders.isEmpty()) {
                statistics.put("taskId", taskId);
                statistics.put("totalFolders", 0);
                statistics.put("successCount", 0);
                statistics.put("partialCount", 0);
                statistics.put("failedCount", 0);
                statistics.put("unparsedCount", 0);
                return statistics;
            }

            // 2. 统计各种状态的数量
            int totalFolders = folders.size();
            int successCount = 0;
            int partialCount = 0;
            int failedCount = 0;
            int unparsedCount = 0;

            for (FolderInfoVO folderVO : folders) {
                try {
                    FolderInfo folderInfo = folderInfoService.getFolderByMongoId(folderVO.getId());
                    if (folderInfo != null && folderInfo.getPreParseVersionInfo() != null) {
                        String parseStatus = folderInfo.getPreParseVersionInfo().getParseStatus();
                        if (StringUtils.hasText(parseStatus)) {
                            switch (parseStatus) {
                                case "SUCCESS":
                                    successCount++;
                                    break;
                                case "PARTIAL":
                                    partialCount++;
                                    break;
                                case "FAILED":
                                    failedCount++;
                                    break;
                                default:
                                    unparsedCount++;
                                    break;
                            }
                        } else {
                            unparsedCount++;
                        }
                    } else {
                        unparsedCount++;
                    }
                } catch (Exception e) {
                    log.error("统计文件夹[{}]解析状态失败: {}", folderVO.getFolderId(), e.getMessage());
                    unparsedCount++;
                }
            }

            statistics.put("taskId", taskId);
            statistics.put("totalFolders", totalFolders);
            statistics.put("successCount", successCount);
            statistics.put("partialCount", partialCount);
            statistics.put("failedCount", failedCount);
            statistics.put("unparsedCount", unparsedCount);

            // 计算百分比
            if (totalFolders > 0) {
                statistics.put("successRate", String.format("%.1f%%", (successCount * 100.0 / totalFolders)));
                statistics.put("partialRate", String.format("%.1f%%", (partialCount * 100.0 / totalFolders)));
                statistics.put("failedRate", String.format("%.1f%%", (failedCount * 100.0 / totalFolders)));
                statistics.put("unparsedRate", String.format("%.1f%%", (unparsedCount * 100.0 / totalFolders)));
            }

            log.debug("任务[{}]解析统计: 总数={}, 成功={}, 部分={}, 失败={}, 未解析={}",
                taskId, totalFolders, successCount, partialCount, failedCount, unparsedCount);

        } catch (Exception e) {
            log.error("获取解析状态统计失败, taskId: {}", taskId, e);
            statistics.put("error", "获取统计失败: " + e.getMessage());
        }

        return statistics;
    }
}
