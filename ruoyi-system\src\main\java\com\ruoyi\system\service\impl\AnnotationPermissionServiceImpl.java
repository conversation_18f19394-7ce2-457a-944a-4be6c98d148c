package com.ruoyi.system.service.impl;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.mongo.FolderInfo;
import com.ruoyi.system.domain.mongo.ImageRepository;
import com.ruoyi.system.domain.mongo.VersionAnnotationTemplate;
import com.ruoyi.system.repository.FolderInfoRepository;
import com.ruoyi.system.repository.ImageRepositoryRepo;
import com.ruoyi.system.service.IAnnotationPermissionService;
import com.ruoyi.system.service.IVersionAnnotationTemplateService;
import com.ruoyi.system.service.IVersionAnnotationTemplateService.AnnotationPermissionInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * 标注权限控制服务实现类
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Service
public class AnnotationPermissionServiceImpl implements IAnnotationPermissionService {
    
    private static final Logger log = LoggerFactory.getLogger(AnnotationPermissionServiceImpl.class);
    
    @Autowired
    private ImageRepositoryRepo imageRepositoryRepo;
    
    @Autowired
    private FolderInfoRepository folderInfoRepository;
    
    @Autowired
    private IVersionAnnotationTemplateService templateService;
    
    @Override
    public boolean canAnnotateImage(String imageId) {
        try {
            return templateService.canAnnotateImage(imageId);
        } catch (Exception e) {
            log.error("检查图片标注权限失败: imageId={}, error={}", imageId, e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public boolean canViewAnnotations(String imageId) {
        try {
            AnnotationPermissionInfo permission = templateService.getAnnotationPermission(imageId);
            return permission.isCanView();
        } catch (Exception e) {
            log.error("检查图片查看权限失败: imageId={}, error={}", imageId, e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public AnnotationPermissionInfo getAnnotationPermission(String imageId) {
        try {
            return templateService.getAnnotationPermission(imageId);
        } catch (Exception e) {
            log.error("获取图片标注权限失败: imageId={}, error={}", imageId, e.getMessage(), e);
            return new AnnotationPermissionInfo(false, false, "权限检查失败: " + e.getMessage());
        }
    }
    
    @Override
    public boolean canSetAsStandardSample(String folderId) {
        try {
            FolderInfo folder = folderInfoRepository.findByFolderId(folderId);
            if (folder == null) {
                return false;
            }
            
            // 如果已经是标准样本，不能再设置
            if ("standard".equals(folder.getFolderType())) {
                return false;
            }
            
            // 检查版本是否已有标准样本
            if (StringUtils.isNotEmpty(folder.getVersionId())) {
                Optional<FolderInfo> existingStandard = folderInfoRepository
                        .findByVersionIdAndFolderType(folder.getVersionId(), "standard");
                return !existingStandard.isPresent();
            }
            
            return true;
            
        } catch (Exception e) {
            log.error("检查是否可设置为标准样本失败: folderId={}, error={}", folderId, e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public boolean canRemoveStandardSample(String folderId) {
        try {
            FolderInfo folder = folderInfoRepository.findByFolderId(folderId);
            if (folder == null) {
                return false;
            }
            
            // 只有标准样本才能取消
            return "standard".equals(folder.getFolderType());
            
        } catch (Exception e) {
            log.error("检查是否可取消标准样本失败: folderId={}, error={}", folderId, e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public boolean validateAnnotationOperation(String imageId, String operation) {
        try {
            AnnotationPermissionInfo permission = getAnnotationPermission(imageId);
            
            switch (operation.toLowerCase()) {
                case "edit":
                case "save":
                case "update":
                    return permission.isCanEdit();
                case "view":
                case "read":
                    return permission.isCanView();
                default:
                    log.warn("未知的标注操作: {}", operation);
                    return false;
            }
            
        } catch (Exception e) {
            log.error("验证标注操作权限失败: imageId={}, operation={}, error={}", 
                    imageId, operation, e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public PermissionCheckResult checkPermissions(String imageId, String userId) {
        try {
            PermissionCheckResult result = new PermissionCheckResult();
            
            // 获取图片信息
            ImageRepository image = imageRepositoryRepo.findByImageId(imageId);
            if (image == null) {
                result.setCanEdit(false);
                result.setCanView(false);
                result.setReason("图片不存在");
                return result;
            }
            
            // 获取文件夹信息
            FolderInfo folder = folderInfoRepository.findByFolderId(image.getFolderId());
            if (folder == null) {
                result.setCanEdit(false);
                result.setCanView(false);
                result.setReason("文件夹不存在");
                return result;
            }
            
            // 设置基本信息
            result.setImageType(image.getImageType());
            result.setStandardSample("standard".equals(folder.getFolderType()));
            
            // 获取标注权限
            AnnotationPermissionInfo annotationPermission = getAnnotationPermission(imageId);
            result.setCanEdit(annotationPermission.isCanEdit());
            result.setCanView(annotationPermission.isCanView());
            result.setReason(annotationPermission.getReason());
            
            // 设置标准样本管理权限
            result.setCanSetStandard(canSetAsStandardSample(image.getFolderId()));
            result.setCanRemoveStandard(canRemoveStandardSample(image.getFolderId()));
            
            return result;
            
        } catch (Exception e) {
            log.error("检查权限失败: imageId={}, userId={}, error={}", imageId, userId, e.getMessage(), e);
            PermissionCheckResult result = new PermissionCheckResult();
            result.setCanEdit(false);
            result.setCanView(false);
            result.setReason("权限检查失败: " + e.getMessage());
            return result;
        }
    }
}
