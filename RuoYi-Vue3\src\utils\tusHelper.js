/**
 * TUS 上传状态帮助工具
 * 
 * 用于处理 TUS 断点续传相关的状态管理和错误恢复
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */

/**
 * 清理所有 TUS 相关的本地存储状态
 */
export function clearTusState() {
  try {
    console.log('开始清理TUS本地状态...')
    
    // 清理localStorage中的tus状态
    const tusKeys = []
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith('tus::')) {
        tusKeys.push(key)
      }
    }
    
    tusKeys.forEach(key => {
      localStorage.removeItem(key)
      console.log(`清理TUS状态: ${key}`)
    })
    
    // 清理sessionStorage中的tus状态
    const sessionTusKeys = []
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i)
      if (key && key.startsWith('tus::')) {
        sessionTusKeys.push(key)
      }
    }
    
    sessionTusKeys.forEach(key => {
      sessionStorage.removeItem(key)
      console.log(`清理TUS会话状态: ${key}`)
    })
    
    console.log(`TUS状态清理完成: localStorage清理${tusKeys.length}项, sessionStorage清理${sessionTusKeys.length}项`)
    
    return {
      success: true,
      clearedLocalStorage: tusKeys.length,
      clearedSessionStorage: sessionTusKeys.length
    }
    
  } catch (error) {
    console.error('清理TUS状态失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 清理指定文件的 TUS 状态
 * @param {string} fileId - 文件ID或上传ID
 */
export function clearFileTusState(fileId) {
  try {
    console.log(`开始清理文件${fileId}的TUS状态...`)
    
    const clearedKeys = []
    
    // 清理localStorage
    for (let i = localStorage.length - 1; i >= 0; i--) {
      const key = localStorage.key(i)
      if (key && key.startsWith('tus::') && key.includes(fileId)) {
        localStorage.removeItem(key)
        clearedKeys.push(key)
        console.log(`清理文件TUS状态: ${key}`)
      }
    }
    
    // 清理sessionStorage
    for (let i = sessionStorage.length - 1; i >= 0; i--) {
      const key = sessionStorage.key(i)
      if (key && key.startsWith('tus::') && key.includes(fileId)) {
        sessionStorage.removeItem(key)
        clearedKeys.push(key)
        console.log(`清理文件TUS会话状态: ${key}`)
      }
    }
    
    console.log(`文件${fileId}的TUS状态清理完成: 清理${clearedKeys.length}项`)
    
    return {
      success: true,
      clearedKeys: clearedKeys.length,
      keys: clearedKeys
    }
    
  } catch (error) {
    console.error(`清理文件${fileId}的TUS状态失败:`, error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 检查是否存在损坏的 TUS 状态
 */
export function checkTusState() {
  try {
    const tusStates = []
    
    // 检查localStorage
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith('tus::')) {
        try {
          const value = localStorage.getItem(key)
          const parsed = JSON.parse(value)
          tusStates.push({
            key,
            storage: 'localStorage',
            ...parsed
          })
        } catch (e) {
          tusStates.push({
            key,
            storage: 'localStorage',
            error: 'Parse error',
            rawValue: localStorage.getItem(key)
          })
        }
      }
    }
    
    // 检查sessionStorage
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i)
      if (key && key.startsWith('tus::')) {
        try {
          const value = sessionStorage.getItem(key)
          const parsed = JSON.parse(value)
          tusStates.push({
            key,
            storage: 'sessionStorage',
            ...parsed
          })
        } catch (e) {
          tusStates.push({
            key,
            storage: 'sessionStorage',
            error: 'Parse error',
            rawValue: sessionStorage.getItem(key)
          })
        }
      }
    }
    
    return {
      success: true,
      states: tusStates,
      count: tusStates.length
    }
    
  } catch (error) {
    console.error('检查TUS状态失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 检测和修复 offset 错误
 * @param {Object} uploadInfo - 上传信息
 */
export function fixOffsetError(uploadInfo) {
  try {
    console.log('检测到offset错误，尝试修复...', uploadInfo)
    
    // 清理相关的TUS状态
    if (uploadInfo.fileId) {
      clearFileTusState(uploadInfo.fileId)
    }
    
    // 也可以尝试清理所有状态
    clearTusState()
    
    console.log('offset错误修复完成，建议重新开始上传')
    
    return {
      success: true,
      message: '已清理损坏的上传状态，请重新选择文件上传'
    }
    
  } catch (error) {
    console.error('修复offset错误失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 获取 TUS 状态统计信息
 */
export function getTusStatistics() {
  try {
    const stats = {
      localStorage: 0,
      sessionStorage: 0,
      totalSize: 0
    }
    
    // 统计localStorage
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith('tus::')) {
        stats.localStorage++
        const value = localStorage.getItem(key)
        stats.totalSize += value ? value.length : 0
      }
    }
    
    // 统计sessionStorage
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i)
      if (key && key.startsWith('tus::')) {
        stats.sessionStorage++
        const value = sessionStorage.getItem(key)
        stats.totalSize += value ? value.length : 0
      }
    }
    
    return {
      success: true,
      ...stats,
      total: stats.localStorage + stats.sessionStorage
    }
    
  } catch (error) {
    console.error('获取TUS统计信息失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

export default {
  clearTusState,
  clearFileTusState,
  checkTusState,
  fixOffsetError,
  getTusStatistics
} 