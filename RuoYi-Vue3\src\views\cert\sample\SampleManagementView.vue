<template>
  <el-container class="sample-management-view">
    <!-- 筛选区 -->
    <el-header height="auto" class="filter-bar">
      <el-form :inline="true" :model="queryParams" @submit.prevent>
        <el-form-item label="文件夹名称">
          <el-input v-model="queryParams.keyword" placeholder="请输入文件夹名称" clearable />
        </el-form-item>
        <el-form-item label="国家">
          <CountrySelect
            v-model="queryParams.countryCode"
            placeholder="请选择国家"
            width="200px"
            @change="handleCountryChange"
          />
        </el-form-item>
        <el-form-item label="证件类型">
          <CertTypeSelect
            v-model="queryParams.certTypeCode"
            placeholder="请选择证件类型"
            width="200px"
            @change="handleCertTypeChange"
          />
        </el-form-item>
        <el-form-item label="年份">
          <el-input v-model="queryParams.issueYear" placeholder="请输入年份" clearable />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option label="未关联" value="unassociated" />
            <el-option label="已关联" value="associated" />
            <el-option label="处理中" value="PROCESSING" />
          </el-select>
        </el-form-item>
        <el-form-item label="解析状态">
          <el-select v-model="queryParams.parseStatus" placeholder="请选择解析状态" clearable>
            <el-option label="已上传" value="UPLOADED" />
            <el-option label="解析中" value="PARSING" />
            <el-option label="已解析" value="PARSED" />
            <el-option label="解析失败" value="PARSE_FAILED" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getFolderList">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-header>

    <!-- 主体表格 -->
    <el-main class="table-container">
      <el-table
        :data="folderList"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      >
        <el-table-column prop="folderName" label="文件夹名称" min-width="200" show-overflow-tooltip />
        <el-table-column prop="countryInfo.name" label="国家" width="120" />
        <el-table-column prop="certInfo.zjlbmc" label="证件类型" width="150" />
        <el-table-column prop="issueYear" label="年份" width="80" />
        <el-table-column prop="fileCount" label="文件数量" width="100" />
        <el-table-column label="状态" width="120">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="解析状态" width="120">
          <template #default="{ row }">
            <el-tag :type="getParseStatusType(row.preParseVersionInfo?.parseStatus)">
              {{ getParseStatusText(row.preParseVersionInfo?.parseStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="关联版本" width="150" show-overflow-tooltip>
          <template #default="{ row }">
            <div v-if="row.versionInfo">
              <el-tag type="success" size="small">{{ row.versionInfo.versionCode }}</el-tag>
              <div class="version-info">
                <el-text type="info" size="small">{{ row.versionInfo.issueYear }}</el-text>
              </div>
            </div>
            <el-tag v-else type="warning" size="small">未关联</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column label="操作" min-width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" type="primary" @click="handleViewDetails(row)" :icon="View">
              查看详情
            </el-button>

            <!-- 版本关联按钮 -->
            <el-button
              v-if="row.status === 'unassociated'"
              size="small"
              type="success"
              @click="handleVersionAssociation(row)"
              :icon="Link"
            >
              版本关联
            </el-button>

            <!-- 更换版本按钮 -->
            <el-button
              v-else
              size="small"
              type="warning"
              @click="handleVersionAssociation(row, true)"
              :icon="Switch"
            >
              更换版本
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-main>

    <!-- 版本关联弹窗 -->
    <SampleVersionAssociationModal
      v-model="showVersionAssociationModal"
      :target-folder="currentFolder"
      :is-change-mode="isChangeMode"
      :current-version="currentVersion"
      @association-changed="handleAssociationChanged"
    />
  </el-container>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { View, Link, Switch } from '@element-plus/icons-vue'

// 导入选择器组件
import CountrySelect from '@/components/CertCommon/CountrySelect.vue'
import CertTypeSelect from '@/components/CertCommon/CertTypeSelect.vue'

// 导入版本关联弹窗
import SampleVersionAssociationModal from './components/SampleVersionAssociationModal.vue'

// 导入API
import { getFolderList as getFolderListApi } from '@/api/cert/folder'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const folderList = ref([])
const total = ref(0)

// 版本关联相关
const showVersionAssociationModal = ref(false)
const currentFolder = ref({})
const isChangeMode = ref(false)
const currentVersion = ref(null)

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  keyword: '',
  countryCode: '',
  certTypeCode: '',
  issueYear: '',
  status: '',
  parseStatus: ''
})

// 获取文件夹列表
async function getFolderList() {
  loading.value = true
  try {
    const filteredParams = {}
    Object.keys(queryParams).forEach(key => {
      const value = queryParams[key]
      if (value !== null && value !== undefined && value !== '') {
        filteredParams[key] = value
      }
    })

    const response = await getFolderListApi(filteredParams)
    if (response.code === 200) {
      folderList.value = response.rows || []
      total.value = response.total || 0
    } else {
      ElMessage.error(response.msg || '获取文件夹列表失败')
    }
  } catch (error) {
    console.error('获取文件夹列表失败:', error)
    ElMessage.error('获取文件夹列表失败')
  } finally {
    loading.value = false
  }
}

// 重置查询条件
function resetQuery() {
  queryParams.pageNum = 1
  queryParams.pageSize = 10
  queryParams.keyword = ''
  queryParams.countryCode = ''
  queryParams.certTypeCode = ''
  queryParams.issueYear = ''
  queryParams.status = ''
  queryParams.parseStatus = ''
  getFolderList()
}

// 分页处理
function handleSizeChange(val) {
  queryParams.pageSize = val
  queryParams.pageNum = 1
  getFolderList()
}

function handleCurrentChange(val) {
  queryParams.pageNum = val
  getFolderList()
}

// 筛选处理
function handleCountryChange(val) {
  queryParams.countryCode = val
  queryParams.pageNum = 1
  getFolderList()
}

function handleCertTypeChange(val) {
  queryParams.certTypeCode = val
  queryParams.pageNum = 1
  getFolderList()
}

// 查看文件夹详情
function viewFolderDetail(row) {
  router.push({
    name: 'FolderDetail',
    params: { folderId: row.folderId }
  })
}

// 查看详情（统一入口）
function handleViewDetails(row) {
  viewFolderDetail(row)
}

// 版本关联处理
function handleVersionAssociation(row, isChange = false) {
  currentFolder.value = row
  isChangeMode.value = isChange

  if (isChange && row.versionInfo) {
    currentVersion.value = row.versionInfo
  } else {
    currentVersion.value = null
  }

  showVersionAssociationModal.value = true
}

// 版本关联成功回调
function handleAssociationChanged() {
  // 刷新列表
  getFolderList()
  ElMessage.success('版本关联操作成功')
}

// 状态显示辅助函数
function getStatusType(status) {
  const statusMap = {
    'unassociated': 'warning',
    'associated': 'success',
    'PROCESSING': 'info'
  }
  return statusMap[status] || 'info'
}

function getStatusText(status) {
  const statusMap = {
    'unassociated': '未关联',
    'associated': '已关联',
    'PROCESSING': '处理中'
  }
  return statusMap[status] || status
}

function getParseStatusType(parseStatus) {
  const statusMap = {
    'UPLOADED': 'info',
    'PARSING': 'warning',
    'PARSED': 'success',
    'PARSE_FAILED': 'danger'
  }
  return statusMap[parseStatus] || 'info'
}

function getParseStatusText(parseStatus) {
  const statusMap = {
    'UPLOADED': '已上传',
    'PARSING': '解析中',
    'PARSED': '已解析',
    'PARSE_FAILED': '解析失败'
  }
  return statusMap[parseStatus] || '未知'
}

onMounted(() => {
  getFolderList()
})
</script>

<style scoped lang="scss">
.sample-management-view {
  height: 100%;
  display: flex;
  flex-direction: column;

  .filter-bar {
    padding: 18px 24px 8px 24px;
    background: #fff;
    border-bottom: 1px solid #e4e7ed;
  }

  .table-container {
    padding: 20px;
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .pagination-wrapper {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }

  .version-info {
    margin-top: 4px;
  }
}
</style>
