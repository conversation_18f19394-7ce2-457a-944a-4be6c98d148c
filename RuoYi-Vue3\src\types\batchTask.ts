/**
 * 批量任务管理相关的TypeScript类型定义
 * 
 * 统一管理所有批量任务相关的接口和类型，确保类型安全
 * 
 * <AUTHOR>
 * @date 2025-07-08
 */

import type { Country, CertType } from './common'

/** 任务状态枚举 */
export enum TaskStatus {
  PENDING = 'PENDING',
  UPLOADING = 'UPLOADING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
  PAUSED = 'PAUSED'
}

/** 解析状态枚举 */
export enum ParseStatus {
  UPLOADED = 'UPLOADED',
  PARSING = 'PARSING',
  PARSED = 'PARSED',
  PARSE_FAILED = 'PARSE_FAILED'
}

/** 文件夹状态枚举 */
export enum FolderStatus {
  UNASSOCIATED = 'unassociated',
  ASSOCIATED = 'associated',
  PROCESSING = 'PROCESSING'
}

/** 批量任务创建请求DTO */
export interface BatchTaskCreateDTO {
  /** 任务名称（可选，如果不提供将自动生成） */
  taskName?: string
  /** 国家ID（必需，用于从MySQL查询Country实体） */
  countryId: number
  /** 证件类型ID（必需，用于从MySQL查询CertType实体） */
  certTypeId: number
  /** 签发年份（必需） */
  issueYear: string
  /** 签发地（可选） */
  issuePlace?: string
  /** 总文件数（可选，用于进度跟踪） */
  totalFiles?: number
  /** 总文件夹数（可选，用于进度跟踪） */
  totalFolders?: number
}

/** 多版本批量任务创建请求DTO */
export interface MultiVersionBatchTaskCreateDTO {
  /** 主任务名称 */
  taskName: string
  /** 任务描述 */
  description?: string
  /** 部门ID */
  deptId: number
  /** 创建者用户名 */
  createdBy: string
  /** 版本列表 */
  versions: VersionInfo[]
}

/** 版本信息 */
export interface VersionInfo {
  /** 文件夹名称 */
  folderName: string
  /** 文件夹路径 */
  folderPath: string
  /** 相对路径 */
  relativePath?: string
  /** 国家ID */
  countryId: number
  /** 证件类型ID */
  certTypeId: number
  /** 签发年份 */
  issueYear: string
  /** 签发地 */
  issuePlace?: string
  /** 证件号前缀 */
  certNumberPrefix?: string
  /** 文件数量 */
  fileCount: number
  /** 文件列表 */
  files: FileInfo[]
  /** 版本索引 */
  versionIndex?: number
  /** 解析状态 */
  parseStatus?: ParseStatus
  /** 元数据 */
  metadata?: VersionMetadata
}

/** 文件信息 */
export interface FileInfo {
  /** 文件名 */
  name: string
  /** 文件大小 */
  size: number
  /** 文件类型 */
  type: string
  /** 最后修改时间 */
  lastModified: number
  /** 相对路径 */
  webkitRelativePath?: string
  /** 文件对象 */
  file?: File
}

/** 版本元数据 */
export interface VersionMetadata {
  /** 证件号前缀 */
  certNumberPrefix: string
  /** 签发地 */
  issuePlace: string
  /** 国家信息 */
  countryInfo?: Country
  /** 证件类型信息 */
  certTypeInfo?: CertType
}

/** 批量任务查询参数 */
export interface BatchTaskQueryParams {
  /** 页码 */
  pageNum?: number
  /** 页大小 */
  pageSize?: number
  /** 任务名称 */
  taskName?: string
  /** 任务状态 */
  status?: TaskStatus | string
  /** 创建者 */
  createdBy?: string
  /** 部门ID */
  deptId?: number
  /** 开始时间 */
  startTime?: string
  /** 结束时间 */
  endTime?: string
  /** 国家代码 */
  countryCode?: string
  /** 证件类型代码 */
  certTypeCode?: string
  /** 签发年份 */
  issueYear?: string
}

/** 批量任务VO */
export interface BatchUploadTaskVO {
  /** 任务ID */
  taskId: string
  /** 任务名称 */
  taskName: string
  /** 任务描述 */
  description?: string
  /** 任务状态 */
  status: TaskStatus
  /** 总文件数 */
  totalFiles: number
  /** 已处理文件数 */
  processedFiles: number
  /** 总文件夹数 */
  totalFolders: number
  /** 已处理文件夹数 */
  processedFolders: number
  /** 创建时间 */
  createTime: string
  /** 更新时间 */
  updateTime?: string
  /** 创建者 */
  createdBy: string
  /** 部门ID */
  deptId: number
  /** 国家信息 */
  countryInfo?: Country
  /** 证件类型信息 */
  certTypeInfo?: CertType
  /** 签发年份 */
  issueYear: string
  /** 签发地 */
  issuePlace?: string
  /** 错误信息 */
  errorMessage?: string
  /** 进度百分比 */
  progressPercentage?: number
}



/** 文件夹信息 */
export interface FolderInfo {
  /** 文件夹ID */
  folderId: string
  /** 文件夹名称 */
  folderName: string
  /** 文件夹路径 */
  folderPath: string
  /** 文件夹状态 */
  status: FolderStatus
  /** 解析状态 */
  parseStatus: ParseStatus
  /** 文件数量 */
  fileCount: number
  /** 创建时间 */
  createTime: string
  /** 国家信息 */
  countryInfo?: Country
  /** 证件类型信息 */
  certTypeInfo?: CertType
  /** 签发年份 */
  issueYear?: string
  /** 签发地 */
  issuePlace?: string
  /** 证件号前缀 */
  certNumberPrefix?: string
  /** 关联的版本ID */
  versionId?: string
  /** 是否为标准样本 */
  isStandardSample?: boolean
}

/** 任务统计信息 */
export interface TaskStatistics {
  /** 总任务数 */
  totalTasks: number
  /** 进行中任务数 */
  runningTasks: number
  /** 已完成任务数 */
  completedTasks: number
  /** 失败任务数 */
  failedTasks: number
  /** 今日新增任务数 */
  todayTasks: number
  /** 本周新增任务数 */
  weekTasks: number
  /** 本月新增任务数 */
  monthTasks: number
}

/** 上传进度信息 */
export interface UploadProgress {
  /** 是否正在上传 */
  isUploading: boolean
  /** 进度百分比 */
  progress: number
  /** 当前上传文件 */
  currentFile?: string
  /** 上传速度 */
  uploadSpeed?: string
  /** 剩余时间 */
  remainingTime?: string
  /** 已上传文件数 */
  uploadedFiles: number
  /** 总文件数 */
  totalFiles: number
  /** 失败文件数 */
  failedFiles: number
}

/** API响应基础接口 */
export interface ApiResponse<T = any> {
  /** 响应代码 */
  code: number
  /** 响应消息 */
  msg: string
  /** 响应数据 */
  data?: T
  /** 分页数据 */
  rows?: T[]
  /** 总数 */
  total?: number
}

/** 分页查询响应 */
export interface PageResponse<T> extends ApiResponse<T> {
  /** 数据列表 */
  rows: T[]
  /** 总记录数 */
  total: number
  /** 当前页码 */
  pageNum: number
  /** 页大小 */
  pageSize: number
}


