package com.ruoyi.system.domain.dto.response;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.system.domain.Country;
import com.ruoyi.system.domain.CertType;
import com.ruoyi.system.domain.mongo.FolderInfo;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 文件夹信息响应VO
 * 用于展示样本文件夹的详情
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
public class FolderInfoVO {
    
    /** MongoDB主键 */
    private String id;
    
    /** 业务文件夹ID */
    private String folderId;
    
    /** 关联任务ID */
    private String taskId;
    
    /** 关联版本ID */
    private String versionId;
    
    /** 文件夹类型 */
    private String folderType;
    
    /** 文件夹名称 */
    private String folderName;

    /** 主图路径 */
    private String mainPicPath;

    /** 国家信息 */
    private Country countryInfo;
    
    /** 证件类型信息 */
    private CertType certInfo;
    
    /** 发行年份 */
    private String issueYear;
    
    /** 文件数量 */
    private Integer fileCount;
    
    /** 已处理文件数量 */
    private Integer processedFileCount;
    
    /** 状态 */
    private String status;

    /** 审核状态 */
    private String reviewStatus;

    /** 版本关联方式 */
    private String versionAssociationMethod;

    /** 版本信息 */
    private VersionInfo versionInfo;

    /** 预解析版本信息 */
    private PreParseVersionInfo preParseVersionInfo;

    /** 部门信息 */
    private SysDept deptInfo;
    
    /** 上传者信息 */
    private SysUser uploaderInfo;
    
    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date updateTime;

    /**
     * 版本信息内嵌类
     */
    @Data
    public static class VersionInfo {
        /** 版本ID */
        private String versionId;

        /** 版本代码 */
        private String versionCode;

        /** 标准样本文件夹ID */
        private String standardFolderId;

        /** 版本状态 */
        private String status;
    }

    /**
     * 预解析版本信息内嵌类
     */
    @Data
    public static class PreParseVersionInfo {
        /** 原始文件夹名称 */
        private String originalFolderName;

        /** 解析后的版本代码 */
        private String parsedVersionCode;

        /** 解析的国家名称 */
        private String countryName;

        /** 匹配的国家代码 */
        private String countryCode;

        /** 解析的证件类型名称 */
        private String certTypeName;

        /** 匹配的证件类型代码 */
        private String certTypeCode;

        /** 签发年份 */
        private String issueYear;

        /** 证件号前缀 */
        private String certNumberPrefix;

        /** 签发地 */
        private String issuePlace;

        /** 解析状态: SUCCESS, PARTIAL, FAILED */
        private String parseStatus;

        /** 解析错误信息 */
        private List<String> parseErrors;

        /** 解析时间 */
        private Date parseTime;
    }
}
