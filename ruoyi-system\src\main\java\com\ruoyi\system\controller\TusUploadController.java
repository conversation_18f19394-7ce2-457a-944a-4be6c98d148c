package com.ruoyi.system.controller;

import com.ruoyi.system.config.TusConfig;
import me.desair.tus.server.TusFileUploadService;
import me.desair.tus.server.upload.UploadInfo;
import me.desair.tus.server.exception.UploadAlreadyLockedException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * Tus断点续传上传控制器
 * 
 * 处理基于Tus协议的文件断点续传上传请求，支持大文件上传和网络中断恢复。
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@RestController
@RequestMapping("/tus")
@CrossOrigin(
    origins = {"http://localhost:80", "http://localhost", "http://127.0.0.1:80"},
    allowCredentials = "true",
    allowedHeaders = {"*", "Tus-Resumable", "Upload-Length", "Upload-Offset", "Upload-Metadata", "Content-Type", "Authorization"},
    exposedHeaders = {"Tus-Resumable", "Tus-Version", "Tus-Max-Size", "Tus-Extension", "Upload-Offset", "Upload-Length", "Location"},
    methods = {RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.DELETE,
               RequestMethod.OPTIONS, RequestMethod.HEAD, RequestMethod.PATCH}
)
public class TusUploadController {

    private static final Logger log = LoggerFactory.getLogger(TusUploadController.class);

    @Autowired
    private TusFileUploadService tusFileUploadService;

    @Autowired
    private TusConfig tusConfig;

    // 添加构造函数来确认控制器被正确初始化
    public TusUploadController() {
        log.info("🚀 TusUploadController 正在初始化...");
    }

    @PostConstruct
    public void init() {
        log.info("✅ TusUploadController 初始化完成，映射路径: /tus");
        log.info("📍 可用端点:");
        log.info("  - GET  /tus/status");
        log.info("  - POST /tus/uploads");
        log.info("  - PATCH /tus/uploads/**");
        log.info("  - HEAD /tus/uploads/**");
        log.info("  - DELETE /tus/uploads/**");
        log.info("  - OPTIONS /tus/uploads/**");
    }

    /**
     * 测试Tus服务状态
     */
    @GetMapping("/status")
    public ResponseEntity<?> getTusStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("service", "Tus Upload Service");
        status.put("status", "running");
        status.put("endpoint", "/tus/uploads");
        status.put("timestamp", System.currentTimeMillis());

        log.info("Tus服务状态检查: {}", status);
        return ResponseEntity.ok(status);
    }

    /**
     * 处理所有Tus协议相关的HTTP请求
     *
     * 支持的HTTP方法：
     * - POST: 创建新的上传任务
     * - PATCH: 上传文件分块数据
     * - HEAD: 查询上传进度
     * - DELETE: 终止上传任务
     * - OPTIONS: 预检请求
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     */
    @RequestMapping(
            value = {"/uploads", "/uploads/**"},
            method = {RequestMethod.POST, RequestMethod.PATCH, RequestMethod.HEAD,
                     RequestMethod.DELETE, RequestMethod.OPTIONS}
    )
    public void handleTusUpload(HttpServletRequest request, HttpServletResponse response) {
        try {
            log.info("处理Tus上传请求: {} {}, URI: {}", request.getMethod(), request.getRequestURI(), request.getRequestURL());

            // 移除认证检查，因为已在 Spring Security 中配置为匿名访问
            String authHeader = request.getHeader("Authorization");
            log.debug("TUS请求认证头: {}", authHeader != null ? "存在" : "不存在");
            
            // 记录关键请求头用于调试
            String tusResumable = request.getHeader("Tus-Resumable");
            String uploadLength = request.getHeader("Upload-Length");
            String uploadOffset = request.getHeader("Upload-Offset");
            String contentType = request.getHeader("Content-Type");
            
            log.info("请求头: Tus-Resumable={}, Upload-Length={}, Upload-Offset={}, Content-Type={}, Auth={}",
                    tusResumable, uploadLength, uploadOffset, contentType, 
                    authHeader != null ? authHeader.substring(0, Math.min(20, authHeader.length())) + "..." : "null");

            // 对于 HEAD 请求，先检查上传状态
            if ("HEAD".equals(request.getMethod())) {
                String uploadUrl = request.getRequestURI();
                try {
                    UploadInfo uploadInfo = tusFileUploadService.getUploadInfo(uploadUrl);
                    if (uploadInfo == null) {
                        log.warn("HEAD请求：找不到上传信息，uploadUrl: {}", uploadUrl);
                        response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                        response.setHeader("Tus-Resumable", "1.0.0");
                        return;
                    }
                    log.info("HEAD请求：上传状态检查，offset={}, length={}, inProgress={}", 
                            uploadInfo.getOffset(), uploadInfo.getLength(), uploadInfo.isUploadInProgress());
                } catch (Exception e) {
                    log.error("HEAD请求：检查上传状态失败，uploadUrl: {}", uploadUrl, e);
                    response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                    response.setHeader("Tus-Resumable", "1.0.0");
                    return;
                }
            }

            // 获取上传URL用于后续处理
            String uploadUrl = request.getRequestURI();

            // 处理Tus协议请求
            tusFileUploadService.process(request, response);

            log.info("Tus请求处理完成，响应状态: {}", response.getStatus());

            // 如果是 POST 请求且成功创建，修改 Location 头
            if ("POST".equals(request.getMethod()) && response.getStatus() == 201) {
                String location = response.getHeader("Location");
                if (location != null && !location.startsWith("/dev-api")) {
                    // 修改 Location 头以包含正确的路径前缀
                    String newLocation = "/dev-api" + location;
                    response.setHeader("Location", newLocation);
                    log.info("修改Location头: {} -> {}", location, newLocation);
                }
            }

            // 在PATCH请求后检查上传是否完成
            if ("PATCH".equals(request.getMethod())) {
                log.info("PATCH请求完成，检查上传状态...");
                // 延迟检查，确保Tus服务状态更新完成并释放文件锁
                new Thread(() -> {
                    try {
                        Thread.sleep(200); // 等待200ms，确保锁释放
                        checkAndHandleUploadCompletion(uploadUrl);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.warn("上传完成检查被中断: {}", uploadUrl);
                    }
                }).start();
            } else if ("POST".equals(request.getMethod())) {
                log.info("POST请求完成，上传已创建");
            }

        } catch (IOException e) {
            log.error("处理Tus上传请求时发生IO异常: {} {}", request.getMethod(), request.getRequestURI(), e);

            // 设置合适的错误响应
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.setHeader("Tus-Resumable", "1.0.0");

            // 如果是 offset 相关错误，返回更具体的错误信息
            if (e.getMessage() != null && e.getMessage().contains("offset")) {
                log.error("检测到offset相关错误，可能是断点续传状态损坏");
                response.setHeader("Tus-Error", "invalid_offset");
            }

        } catch (Exception e) {
            // 检查是否是锁定异常（可能被包装在其他异常中）
            if (isUploadLockedException(e)) {
                log.warn("上传文件已被锁定，可能存在并发上传: {} {}", request.getMethod(), request.getRequestURI());

                // 返回 423 Locked 状态码，表示资源被锁定
                response.setStatus(423); // HTTP 423 Locked
                response.setHeader("Tus-Resumable", "1.0.0");
                response.setHeader("Retry-After", "1"); // 建议1秒后重试

                // 添加自定义错误头，前端可以据此进行重试
                response.setHeader("Tus-Error", "upload_locked");

            } else {
                log.error("处理Tus上传请求时发生未知异常: {} {}", request.getMethod(), request.getRequestURI(), e);
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.setHeader("Tus-Resumable", "1.0.0");
                response.setHeader("Tus-Error", "server_error");
            }
        }
    }

    /**
     * 检查异常是否为上传锁定异常
     *
     * @param e 异常对象
     * @return 如果是锁定异常返回true，否则返回false
     */
    private boolean isUploadLockedException(Exception e) {
        // 检查异常类型
        if (e instanceof UploadAlreadyLockedException) {
            return true;
        }

        // 检查异常消息
        String message = e.getMessage();
        if (message != null && (
            message.contains("already locked") ||
            message.contains("UploadAlreadyLockedException") ||
            message.contains("Unable to lock upload")
        )) {
            return true;
        }

        // 检查异常原因链
        Throwable cause = e.getCause();
        while (cause != null) {
            if (cause instanceof UploadAlreadyLockedException) {
                return true;
            }
            String causeMessage = cause.getMessage();
            if (causeMessage != null && (
                causeMessage.contains("already locked") ||
                causeMessage.contains("UploadAlreadyLockedException") ||
                causeMessage.contains("Unable to lock upload")
            )) {
                return true;
            }
            cause = cause.getCause();
        }

        return false;
    }

    /**
     * 检查并处理上传完成
     *
     * 使用正确的Tus上传完成检测方案：
     * 1. 调用 tusFileUploadService.getUploadInfo(uploadUrl) 获取上传状态
     * 2. 当 UploadInfo 不为 null 且 !info.isUploadInProgress() 时，表示上传完成
     * 3. 执行业务处理逻辑并清理临时文件
     *
     * @param uploadUrl 上传URL
     */
    private void checkAndHandleUploadCompletion(String uploadUrl) {
        try {
            // 获取上传信息，处理锁冲突
            UploadInfo uploadInfo;
            try {
                uploadInfo = tusFileUploadService.getUploadInfo(uploadUrl);
            } catch (UploadAlreadyLockedException e) {
                log.debug("上传文件正在被其他线程处理，跳过检查: {}", uploadUrl);
                return;
            }

            if (uploadInfo != null && !uploadInfo.isUploadInProgress()) {
                log.info("检测到文件上传完成: uploadUrl={}, length={}, offset={}, lengthType={}, offsetType={}",
                        uploadUrl, uploadInfo.getLength(), uploadInfo.getOffset(),
                        uploadInfo.getLength() != null ? uploadInfo.getLength().getClass().getSimpleName() : "null",
                        Long.valueOf(uploadInfo.getOffset()).getClass().getSimpleName());

                // 检查上传是否真正完成（offset == length）
                Long length = uploadInfo.getLength();
                long offset = uploadInfo.getOffset();

                if (length != null && offset == length.longValue()) {
                    log.info("文件上传确认完成，开始业务处理: {}", uploadUrl);

                    // 执行上传完成的业务处理
                    handleUploadCompletion(uploadUrl, uploadInfo);

                    // 清理临时文件
                    tusFileUploadService.deleteUpload(uploadUrl);
                    log.info("已清理临时文件: {}", uploadUrl);
                } else {
                    log.warn("上传状态异常: uploadUrl={}, offset={}, length={}, lengthIsNull={}",
                            uploadUrl, offset, length, length == null);
                }
            } else {
                log.debug("文件上传进行中: uploadUrl={}", uploadUrl);
            }

        } catch (UploadAlreadyLockedException e) {
            log.debug("上传文件被锁定，跳过检查: {}", uploadUrl);
        } catch (Exception e) {
            log.error("检查上传完成状态时发生异常: {}", uploadUrl, e);
        }
    }

    /**
     * 处理上传完成事件
     *
     * @param uploadUrl 上传URL
     * @param uploadInfo 上传信息对象
     */
    private void handleUploadCompletion(String uploadUrl, UploadInfo uploadInfo) {
        try {
            log.info("开始处理上传完成事件，uploadUrl: {}", uploadUrl);

            // 获取文件流进行业务处理
            try (InputStream inputStream = tusFileUploadService.getUploadedBytes(uploadUrl)) {
                if (inputStream != null) {
                    // 调用配置类中的处理方法
                    tusConfig.handleUploadCompletion(uploadInfo, inputStream);
                    log.info("上传完成事件处理成功，uploadUrl: {}", uploadUrl);
                } else {
                    log.error("无法获取上传文件流，uploadUrl: {}", uploadUrl);
                }
            }

        } catch (Exception e) {
            log.error("处理上传完成事件时发生异常，uploadUrl: {}", uploadUrl, e);
            // 这里可以考虑将失败的上传记录到数据库中，以便后续重试
        }
    }

    /**
     * 获取上传信息API
     * 
     * @param uploadId 上传ID
     * @return 上传信息
     */
    @GetMapping("/uploads/{uploadId}/info")
    public ResponseEntity<?> getUploadInfo(@PathVariable String uploadId) {
        try {
            String uploadUrl = "/tus/uploads/" + uploadId;
            UploadInfo uploadInfo = tusFileUploadService.getUploadInfo(uploadUrl);
            
            if (uploadInfo == null) {
                return ResponseEntity.notFound().build();
            }
            
            // 构建响应数据
            Map<String, Object> response = new HashMap<>();
            response.put("uploadId", uploadInfo.getId().toString());
            response.put("length", uploadInfo.getLength() != null ? uploadInfo.getLength() : 0);
//            response.put("offset", tusFileUploadService.getUploadOffset(uploadUrl));
            response.put("metadata", uploadInfo.getMetadata());
            response.put("creationTimestamp", uploadInfo.getCreationTimestamp());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取上传信息时发生异常，uploadId: {}", uploadId, e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "获取上传信息失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(errorResponse);
        }
    }

    /**
     * 手动触发上传完成处理
     * 
     * 用于处理可能遗漏的上传完成事件
     * 
     * @param uploadId 上传ID
     * @return 处理结果
     */
//    @PostMapping("/uploads/{uploadId}/complete")
//    public ResponseEntity<?> manualCompleteUpload(@PathVariable String uploadId) {
//        try {
//            String uploadUrl = "/api/tus/uploads/" + uploadId;
//
//            // 检查上传是否真的完成
//            if (!isUploadCompleted(null, uploadUrl)) {
//                return ResponseEntity.badRequest()
//                        .body(Map.of("error", "上传尚未完成，无法手动触发完成处理"));
//            }
//
//            // 处理上传完成
//            handleUploadCompletion(uploadUrl);
//
//            return ResponseEntity.ok(Map.of("message", "上传完成处理成功"));
//
//        } catch (Exception e) {
//            log.error("手动触发上传完成处理时发生异常，uploadId: {}", uploadId, e);
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
//                    .body(Map.of("error", "处理失败: " + e.getMessage()));
//        }
//    }

    /**
     * 清理过期上传
     * 
     * @return 清理结果
     */
    @PostMapping("/cleanup")
    public ResponseEntity<?> cleanupExpiredUploads() {
        try {
            log.info("开始清理过期的Tus上传");
            
            // 调用Tus服务的清理方法
            tusFileUploadService.cleanup();
            
            log.info("过期上传清理完成");
            Map<String, Object> successResponse = new HashMap<>();
            successResponse.put("message", "过期上传清理完成");
            return ResponseEntity.ok(successResponse);
            
        } catch (Exception e) {
            log.error("清理过期上传时发生异常", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "清理失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(errorResponse);
        }
    }
}
